import { createApp } from 'vue'
import { createPinia } from 'pinia'

// 引入全局 css
import '@/styles/normalize.css'

// 导入Unocss样式 https://uno.antfu.me
import 'uno.css'

// Arco Design
import ArcoVue from '@arco-design/web-vue'
import '@arco-design/web-vue/dist/arco.css'

// 引入 Pinia 状态持久化插件
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

import App from './App.vue'
import router from './router'

// 引入权限控制
import './permission'

const app = createApp(App)

// 创建 Pinia 实例
const pinia = createPinia()
// 使用 Pinia 状态持久化插件
pinia.use(piniaPluginPersistedstate)

app.use(pinia)
app.use(router)
app.use(ArcoVue)

app.mount('#app')
