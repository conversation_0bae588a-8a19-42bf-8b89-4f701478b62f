import type { RouteRecordRaw } from 'vue-router'

/**
 * @description 菜单路由数组格式化处理
 * 主要功能：
 * 1. 规范化路由路径：将相对路径转换为绝对路径（如 'devtools' -> '/devtools'）
 * 2. 递归处理子路由：为嵌套路由构建完整的路径层级
 * 3. 合并路由元信息：将 meta 属性合并到路由对象中
 * 注意：此函数会修改原始路由对象，建议传入副本以保持数据不可变性
 * @param router 路由数组
 * @param parentPath 父级路由 path
 * @return 格式化后的路由数组
 */
export const menuRouterFormat = (
  router: RouteRecordRaw[],
  parentPath?: string
): RouteRecordRaw[] => {
  return router.map(item => {
    // 拼接路由，例：'devtools' -> '/devtools'  'regular' -> '/devtools/regular'  '/dashboard' -> '/dashboard'
    if (!item.path.startsWith('/')) {
      item.path = parentPath ? `${parentPath}/${item.path}` : `/${item.path}`
    }

    // 存在 children 属性，且 children 数组长度大于 0，开始递归
    if (item.children && item.children.length > 0) {
      item.children = menuRouterFormat(item.children, item.path)
    }

    return Object.assign({}, item, item.meta || {})
  })
}
