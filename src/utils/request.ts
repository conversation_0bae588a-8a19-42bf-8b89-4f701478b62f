/**
 * HTTP 请求工具模块
 *
 * 功能说明：
 * - 基于 axios 创建统一的 HTTP 请求实例
 * - 配置请求和响应拦截器
 * - 统一处理请求头设置（Content-Type: application/json）
 * - 统一处理业务响应码（成功返回 data.data，失败显示错误信息）
 * - 统一处理 HTTP 状态码错误（401、403、404、500 等）
 * - 统一处理网络错误和请求配置错误
 * - 所有错误信息通过 AMessage 组件统一显示，支持手动关闭
 */

import axios from 'axios'
import { useUserStore } from '@/stores/user'

import router from '@/router'

// 错误消息去重处理（防止并发请求产生重复错误提示）
let lastError: string | null = null
let lastErrorTimestamp = 0

/**
 * 显示错误消息
 * @param message 错误消息内容
 * @param type 消息类型，默认为'error'
 */
const showMessage = (message: string, type: 'error' | 'warning' = 'error') => {
  if (type === 'error') {
    AMessage.error({
      content: message,
      closable: true
    })
  } else {
    AMessage.warning({
      content: message,
      closable: true
    })
  }
}

// 创建 axios 实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 设置请求头 Content-Type 为 JSON
    config.headers['Content-Type'] = 'application/json'

    // 添加 Authorization 头
    const userStore = useUserStore()
    if (userStore.isLoggedIn && userStore.token) {
      config.headers['Authorization'] = `Bearer ${userStore.token}`
    }

    return config
  },
  error => {
    showMessage('请求错误')
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    const { data } = response

    // 业务逻辑成功
    if (data.code === 200) {
      return data
    } else {
      // 业务逻辑失败
      showMessage(data.message || '请求失败')
      return Promise.reject(new Error(data.message || '请求失败'))
    }
  },
  error => {
    const currentTime = Date.now()
    const { message } = error

    /**
     * 错误消息去重判断
     * 防止并发请求失败时显示多个相同的错误提示
     * 只有当错误信息与上次不同，或者时间间隔超过2秒时才显示
     */
    if (message !== lastError || currentTime - lastErrorTimestamp > 2000) {
      // 更新错误记录
      lastError = message
      lastErrorTimestamp = currentTime

      /**
       * 错误类型分类处理
       * Axios 错误分为三种情况：
       * 1. error.response 存在：HTTP响应错误（服务器有响应，但状态码表示错误）
       * 2. error.request 存在：网络错误（请求已发出，但没有收到响应）
       * 3. 都不存在：请求配置错误（请求还没发出就出错了）
       */
      if (error.response) {
        /**
         * HTTP响应错误处理
         * 服务器收到请求并返回了响应，但HTTP状态码表示错误
         * 如：401未授权、404未找到、500服务器错误等
         */
        const { status, data } = error.response

        switch (status) {
          case 401:
            // 身份验证失败，Token过期或无效
            showMessage('Token 已过期，请重新登录', 'warning')
            // 清除用户数据并跳转到登录页
            const userStore = useUserStore()
            userStore.clearUserData()
            router.push('/login')
            break
          case 403:
            // 权限不足，用户没有访问该资源的权限
            showMessage('权限不足')
            break
          case 404:
            // 资源未找到，API接口不存在或路径错误
            showMessage('请求的资源不存在')
            break
          case 500:
            // 服务器内部错误，需要跳转到500错误页面
            showMessage('服务器内部错误')
            // 保存当前路由到sessionStorage，用于500页面重试成功后跳转回来
            const currentRoute = router.currentRoute.value.fullPath
            if (currentRoute !== '/500') {
              sessionStorage.setItem('lastRoute', currentRoute)
            }
            router.push('/500')
            break
          default:
            // 其他HTTP错误状态码，显示服务器返回的错误信息或默认信息
            showMessage(data?.message || `请求失败: ${status}`)
        }

        return Promise.reject(error)
      } else if (error.request) {
        /**
         * 网络错误处理
         * 请求已发出，但没有收到任何响应
         * 常见情况：网络断开、服务器宕机、请求超时、DNS解析失败等
         */
        showMessage('网络错误，请检查网络连接')
        return Promise.reject(new Error('网络错误'))
      } else {
        /**
         * 请求配置错误处理
         * 请求还没发出就出错了，通常是请求配置有问题
         * 常见情况：URL格式错误、请求方法配置错误、拦截器中的代码出错等
         */
        showMessage('请求配置错误')
        return Promise.reject(error)
      }
    } else {
      /**
       * 重复错误处理
       * 当错误信息与上次相同且时间间隔小于2秒时，
       * 不显示错误提示，但仍然返回拒绝的Promise
       */
      return Promise.reject(error)
    }
  }
)

export default request
