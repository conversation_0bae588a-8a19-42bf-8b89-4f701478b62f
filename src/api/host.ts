import request from '@/utils/request'
import type { HostRecord } from '@/types/host'
import type { ApiResponse } from '@/types/global'

/**
 * 主机列表查询参数接口
 */
export interface HostListParams {
  page?: number
  pageSize?: number
  hostname?: string
  ipAddress?: string
  operatingSystem?: string
}

/**
 * 主机列表响应数据接口
 */
export interface RespHostListData {
  hosts: HostRecord[]
  total: number
}

/**
 * 获取主机列表
 * @param params 查询参数
 * @returns 主机列表数据
 */
export function getHostListApi(
  params: HostListParams = {}
): Promise<ApiResponse<RespHostListData>> {
  return request({
    url: '/hosts/list',
    method: 'GET',
    params
  })
}

/**
 * 创建主机
 * @param hostData 主机数据
 * @returns 创建结果
 */
export function createHostApi(
  hostData: Partial<HostRecord>
): Promise<ApiResponse<null>> {
  return request({
    url: '/hosts/create',
    method: 'POST',
    data: hostData
  })
}

/**
 * 获取主机信息
 * @param hostId 主机ID
 * @returns 主机信息
 */
export function getHostInfoApi(
  hostId: string
): Promise<ApiResponse<HostRecord>> {
  return request({
    url: '/hosts/info',
    method: 'GET',
    params: { hostId }
  })
}

/**
 * 更新主机信息
 * @param hostId 主机ID
 * @param hostData 更新的主机数据
 * @returns 更新结果
 */
export function updateHostApi(
  hostId: string,
  hostData: Partial<HostRecord>
): Promise<ApiResponse<null>> {
  return request({
    url: '/hosts/update',
    method: 'PUT',
    params: { hostId },
    data: hostData
  })
}

/**
 * 删除主机
 * @param hostId 主机ID
 * @returns 删除结果
 */
export function deleteHostApi(hostId: string): Promise<ApiResponse<null>> {
  return request({
    url: '/hosts/delete',
    method: 'DELETE',
    params: { hostId }
  })
}
