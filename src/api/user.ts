import request from '@/utils/request'
import type { UserRecord } from '@/types/user'
import type { ApiResponse } from '@/types/global'

/**
 * 用户列表查询参数接口
 */
export interface UserListParams {
  page?: number
  pageSize?: number
  displayName?: string
  username?: string
  groupId?: string
  phone?: string
  authType?: string
  status?: string
}

/**
 * 用户列表响应数据接口
 */
export interface RespUserListData {
  users: UserRecord[] // 改为小写，与实际API响应匹配
  total: number
}

/**
 * 获取用户列表
 * @param params 查询参数
 * @returns 用户列表数据
 */
export function getUserListApi(
  params: UserListParams = {}
): Promise<ApiResponse<RespUserListData>> {
  return request({
    url: '/users/list',
    method: 'GET',
    params
  })
}

/**
 * 创建用户
 * @param userData 用户数据
 * @returns 创建结果
 */
export function createUserApi(
  userData: Partial<UserRecord>
): Promise<ApiResponse<null>> {
  return request({
    url: '/users/create',
    method: 'POST',
    data: userData
  })
}

/**
 * 获取用户信息
 * @param userid 用户ID
 * @returns 用户信息
 */
export function getUserInfoApi(
  userid: string
): Promise<ApiResponse<UserRecord>> {
  return request({
    url: '/users/info',
    method: 'GET',
    params: { userid }
  })
}

/**
 * 更新用户信息
 * @param userid 用户ID
 * @param userData 更新的用户数据
 * @returns 更新结果
 */
export function updateUserApi(
  userid: string,
  userData: Partial<UserRecord>
): Promise<ApiResponse<null>> {
  return request({
    url: '/users/update',
    method: 'PUT',
    params: { userid },
    data: userData
  })
}

/**
 * 删除用户
 * @param userid 用户ID
 * @returns 删除结果
 */
export function deleteUserApi(userid: string): Promise<ApiResponse<null>> {
  return request({
    url: '/users/delete',
    method: 'DELETE',
    params: { userid }
  })
}

/**
 * 启用用户
 * @param userid 用户ID
 * @returns 操作结果
 */
export function enableUserApi(userid: string): Promise<ApiResponse<null>> {
  return request({
    url: '/users/enable',
    method: 'PATCH',
    params: { userid }
  })
}

/**
 * 禁用用户
 * @param userid 用户ID
 * @returns 操作结果
 */
export function disableUserApi(userid: string): Promise<ApiResponse<null>> {
  return request({
    url: '/users/disable',
    method: 'PATCH',
    params: { userid }
  })
}
