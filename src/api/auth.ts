import request from '@/utils/request'
import type { ApiResponse } from '@/types/global'
import type { UserRecord } from '@/types/user'

/**
 * 登录表单接口
 */
export interface LoginForm {
  username: string
  password: string
  authType: string
}

/**
 * 登录响应数据接口
 */
export interface LoginResponse {
  token: string
}

/**
 * 用户登录
 * @param data 登录表单数据
 * @returns 登录响应数据
 */
export function loginApi(data: LoginForm): Promise<ApiResponse<LoginResponse>> {
  return request({
    url: '/auth/login',
    method: 'POST',
    data
  })
}

/**
 * 获取用户信息
 * @returns 用户信息数据
 */
export function getUserProfileApi(): Promise<ApiResponse<UserRecord>> {
  return request({
    url: '/auth/profile',
    method: 'GET'
  })
}
