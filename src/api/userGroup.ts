/**
 * 用户组管理相关API接口
 * 提供用户组的增删改查和成员管理功能的网络请求封装
 */

import request from '@/utils/request'
import type {
  UserGroupRecord,
  CreateUserGroupRequest,
  UpdateUserGroupRequest,
  UserGroupListParams,
  RespUserGroupListData
} from '@/types/userGroup'
import type { ApiResponse } from '@/types/global'

// ==================== 用户组基础CRUD操作 ====================

/**
 * 获取用户组列表
 * 支持分页查询和按名称、编码进行模糊搜索
 * @param params 查询参数，包含分页和搜索条件
 * @returns Promise<用户组列表响应数据>
 */
export function getUserGroupListApi(
  params: UserGroupListParams = {}
): Promise<ApiResponse<RespUserGroupListData>> {
  return request({
    url: '/user-groups/list',
    method: 'GET',
    params
  })
}

/**
 * 根据ID获取用户组详情
 * 获取指定用户组的完整信息，包含关联的用户列表
 * @param groupid 用户组ID
 * @returns Promise<用户组详情数据>
 */
export function getUserGroupInfoApi(
  groupid: string
): Promise<ApiResponse<UserGroupRecord>> {
  return request({
    url: '/user-groups/info',
    method: 'GET',
    params: { groupid }
  })
}

/**
 * 创建新用户组
 * 创建一个新的用户组，验证名称和编码的唯一性
 * @param data 用户组创建数据
 * @returns Promise<创建的用户组数据>
 */
export function createUserGroupApi(
  data: CreateUserGroupRequest
): Promise<ApiResponse<UserGroupRecord>> {
  return request({
    url: '/user-groups/create',
    method: 'POST',
    data
  })
}

/**
 * 更新用户组信息
 * 更新指定用户组的基本信息（不包含成员关系）
 * @param groupid 用户组ID
 * @param data 用户组更新数据
 * @returns Promise<更新后的用户组数据>
 */
export function updateUserGroupApi(
  groupid: string,
  data: UpdateUserGroupRequest
): Promise<ApiResponse<UserGroupRecord>> {
  return request({
    url: '/user-groups/update',
    method: 'PUT',
    params: { groupid },
    data
  })
}

/**
 * 删除用户组
 * 永久删除指定的用户组，同时解除所有用户关联关系
 * @param groupid 用户组ID
 * @returns Promise<删除操作结果>
 */
export function deleteUserGroupApi(
  groupid: string
): Promise<ApiResponse<null>> {
  return request({
    url: '/user-groups/delete',
    method: 'DELETE',
    params: { groupid }
  })
}
