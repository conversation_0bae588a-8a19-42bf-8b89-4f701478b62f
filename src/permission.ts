/**
 * 权限控制模块
 *
 * 功能说明：
 * - 统一管理路由权限控制
 * - 基于token的身份验证
 * - 白名单路由管理
 * - 自动跳转到登录页
 */

import router from '@/router'
import { useUserStore } from '@/stores/user'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置 NProgress
NProgress.configure({
  showSpinner: false, // 不显示右上角螺旋加载提示
  easing: 'ease', // 动画方式：缓动效果
  speed: 300, // 递增进度条的速度
  minimum: 0.3 // 更改启动时使用的最小百分比
})

/**
 * 白名单路由（不需要登录验证的路由）
 */
const whiteList = ['/login', '/500', '/404']

// 全局前置守卫
router.beforeEach(async (to, _from, next) => {
  // 开始进度条
  NProgress.start()

  const userStore = useUserStore()

  // 如果是白名单路由，直接放行
  if (whiteList.includes(to.path)) {
    next()
    return
  }

  // 检查是否已登录且token有效
  if (userStore.isLoggedIn && userStore.checkTokenValid()) {
    // 已登录且token有效，检查是否需要获取用户信息
    if (!userStore.userInfo) {
      try {
        // 用户信息不存在，自动获取
        await userStore.getUserInfo()
        // 重要：使用 replace 模式重新导航，避免导航循环
        next({ ...to, replace: true })
      } catch (error) {
        // 获取用户信息失败，响应拦截器已处理401错误（清除数据+跳转）
        // 取消当前导航，让响应拦截器的跳转生效
        console.error('获取用户信息失败:', error)
        next(false)
      }
    } else {
      // 用户信息存在，直接放行
      next()
    }
  } else {
    // 未登录或token无效，清除用户数据并跳转到登录页
    userStore.clearUserData()
    next('/login')
  }
})

// 全局后置钩子
router.afterEach(() => {
  // 结束进度条
  NProgress.done()
})

/**
 * 获取当前白名单
 * @returns 白名单路由数组的副本
 */
export function getWhiteList(): string[] {
  return [...whiteList]
}
