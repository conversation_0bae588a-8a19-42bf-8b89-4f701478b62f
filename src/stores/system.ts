import IconMaterialSymbolsWbSunnyRounded from '~icons/material-symbols/wb-sunny-rounded'
import IconMaterialSymbolsDarkModeRounded from '~icons/material-symbols/dark-mode-rounded'
import IconMaterialSymbolsComputer from '~icons/material-symbols/computer'

export const useSystemStore = defineStore(
  'system',
  () => {
    // 模式列表
    const modeList = ref([
      {
        name: 'auto',
        icon: markRaw(IconMaterialSymbolsComputer),
        title: '自动模式'
      },
      {
        name: 'light',
        icon: markRaw(IconMaterialSymbolsWbSunnyRounded),
        title: '亮色模式'
      },
      {
        name: 'dark',
        icon: markRaw(IconMaterialSymbolsDarkModeRounded),
        title: '暗色模式'
      }
    ])

    // 当前选中的主题模式
    const currentMode = ref<{ name: string; icon: any; title: string } | null>(
      null
    )

    // 应用主题到 body
    const applyTheme = (themeName: string) => {
      if (themeName === 'dark') {
        document.body.setAttribute('arco-theme', 'dark')
      } else if (themeName === 'light') {
        document.body.removeAttribute('arco-theme')
      } else if (themeName === 'auto') {
        // 自动模式根据系统偏好设置
        const prefersDark = window.matchMedia(
          '(prefers-color-scheme: dark)'
        ).matches
        if (prefersDark) {
          document.body.setAttribute('arco-theme', 'dark')
        } else {
          document.body.removeAttribute('arco-theme')
        }
      }
    }

    // 监听模式变化，自动应用主题
    watch(
      currentMode,
      newMode => {
        if (newMode) {
          applyTheme(newMode.name)
        }
      },
      { immediate: true }
    )

    // 初始化模式 - 处理从本地存储恢复的数据
    // 作用：确保 currentMode 引用的是 modeList 中的完整对象，而不是序列化后的简单对象
    // 原因：Pinia 持久化只保存数据，不保存 markRaw 包装的图标组件引用
    const initMode = () => {
      if (!currentMode.value) {
        // 首次访问，设置默认模式（自动模式）
        currentMode.value = modeList.value[0]
      } else {
        // 从本地存储恢复后，重新关联到 modeList 中包含图标的完整对象
        currentMode.value =
          modeList.value.find(item => item.name === currentMode.value?.name) ||
          modeList.value[0]
      }
    }

    return {
      currentMode,
      modeList,
      initMode,
      applyTheme
    }
  },
  {
    persist: {
      key: 'system-store',
      pick: ['currentMode']
    }
  }
)
