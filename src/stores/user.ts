import { defineStore } from 'pinia'
import { ref } from 'vue'
import { loginApi, getUserProfileApi, type LoginForm } from '@/api/auth'
import type { UserRecord } from '@/types/user'
import router from '@/router'

/**
 * 用户信息接口（兼容旧版本）
 */
export interface UserInfo {
  userId?: string
  username?: string
  email?: string
  avatar?: string
  roles?: string[]
}

export const useUserStore = defineStore(
  'user',
  () => {
    // 用户token
    const token = ref<string>('')

    // 用户信息
    const userInfo = ref<UserRecord | null>(null)

    // 是否已登录
    const isLoggedIn = computed(() => !!token.value)

    /**
     * 登录方法
     * @param loginForm 登录表单数据
     * @returns Promise<ApiResponse<{ token: string }>>
     */
    const login = async (loginForm: LoginForm) => {
      try {
        const response = await loginApi(loginForm)

        // 请求拦截器已经处理了状态码检查，能执行到这里说明登录成功
        // 直接保存token
        token.value = response.data.token

        // 登录成功后获取用户信息
        await getUserInfo()

        return response
      } catch (error) {
        // 清除可能存在的无效token
        clearUserData()
        throw error
      }
    }

    /**
     * 登出方法
     */
    const logout = () => {
      clearUserData()

      // 跳转到登录页
      router.push('/login')

      AMessage.success({ content: '退出登录' })
    }

    /**
     * 获取用户信息
     */
    const getUserInfo = async () => {
      try {
        const response = await getUserProfileApi()
        userInfo.value = response.data
        return response
      } catch (error) {
        console.error('获取用户信息失败:', error)
        throw error
      }
    }

    /**
     * 清除用户数据
     */
    const clearUserData = () => {
      token.value = ''
      userInfo.value = null
      // 清除localStorage中的数据
      localStorage.removeItem('user-store')
    }

    /**
     * 检查token是否有效
     * @returns boolean
     */
    const checkTokenValid = (): boolean => {
      if (!token.value) return false

      try {
        // 简单的JWT token检查（检查是否过期）
        const payload = JSON.parse(atob(token.value.split('.')[1]))
        const currentTime = Math.floor(Date.now() / 1000)

        if (payload.exp && payload.exp < currentTime) {
          // token已过期
          clearUserData()
          return false
        }

        return true
      } catch {
        // token格式错误
        clearUserData()
        return false
      }
    }

    return {
      token,
      userInfo,
      isLoggedIn,
      login,
      logout,
      getUserInfo,
      clearUserData,
      checkTokenValid
    }
  },
  {
    persist: {
      key: 'user-store',
      storage: localStorage,
      pick: ['token']
    }
  }
)
