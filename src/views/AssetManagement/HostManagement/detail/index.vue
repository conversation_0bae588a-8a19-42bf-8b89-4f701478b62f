<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Message as AMessage } from '@arco-design/web-vue'
import type { HostRecord } from '@/types/host'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 主机详情数据
const hostDetail = ref<HostRecord>()

// 加载状态
const loading = ref(false)

/**
 * 获取主机详情数据
 */
const loadHostDetail = async () => {
  const hostId = route.params.id as string
  if (!hostId) {
    AMessage.error('主机ID不存在')
    router.back()
    return
  }

  loading.value = true
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    // 模拟数据 - 实际项目中应该调用API
    const mockHostData: HostRecord[] = [
      {
        id: '1',
        hostname: 'web-server-01',
        ipAddress: '************',
        cpu: 6,
        memory: 16,
        disks: [
          { name: 'sda', size: 256, type: 'ssd' },
          { name: 'sdb', size: 512, type: 'ssd' }
        ],
        operatingSystem: 'Ubuntu 20.04 LTS',
        remarks: 'Web服务器，运行Nginx和Node.js应用',
        createTime: '2023-10-01 10:30:00',
        updateTime: '2024-01-15 14:20:00'
      },
      {
        id: '2',
        hostname: 'db-server-01',
        ipAddress: '************',
        cpu: 8,
        memory: 32,
        disks: [
          { name: 'sda', size: 500, type: 'ssd' },
          { name: 'sdb', size: 1024, type: 'ssd' },
          { name: 'sdc', size: 2048, type: 'hdd' }
        ],
        operatingSystem: 'CentOS 7.9',
        remarks: '数据库服务器，MySQL主节点',
        createTime: '2023-09-15 09:15:00',
        updateTime: '2024-01-10 11:45:00'
      },
      {
        id: '3',
        hostname: 'backup-server-01',
        ipAddress: '************',
        cpu: 8,
        memory: 64,
        disks: [
          { name: 'C盘', size: 256, type: 'ssd' },
          { name: 'D盘', size: 2048, type: 'hdd' },
          { name: 'E盘', size: 4096, type: 'hdd' }
        ],
        operatingSystem: 'Windows Server 2019',
        remarks: '备份服务器，定时备份重要数据',
        createTime: '2023-08-20 16:00:00',
        updateTime: '2023-12-25 08:30:00'
      }
    ]

    const foundHost = mockHostData.find(host => host.id === hostId)
    if (foundHost) {
      hostDetail.value = foundHost
    } else {
      AMessage.error('主机不存在')
      router.back()
    }
  } catch (error) {
    console.error('加载主机详情失败:', error)
    AMessage.error('加载主机详情失败')
    router.back()
  } finally {
    loading.value = false
  }
}

/**
 * 计算硬盘总容量
 */
const totalDiskSize = computed(() => {
  if (!hostDetail.value?.disks) return 0
  return hostDetail.value.disks.reduce((total, disk) => total + disk.size, 0)
})

/**
 * 计算SSD硬盘数量
 */
const ssdCount = computed(() => {
  if (!hostDetail.value?.disks) return 0
  return hostDetail.value.disks.filter(disk => disk.type === 'ssd').length
})

/**
 * 计算HDD硬盘数量
 */
const hddCount = computed(() => {
  if (!hostDetail.value?.disks) return 0
  return hostDetail.value.disks.filter(disk => disk.type === 'hdd').length
})

/**
 * 返回列表页面
 */
const handleBack = () => {
  router.back()
}

/**
 * 编辑主机
 */
const handleEdit = () => {
  AMessage.info('编辑功能开发中...')
}

// 页面加载时获取数据
onMounted(() => {
  loadHostDetail()
})
</script>

<template>
  <div class="host-detail-page">
    <!-- 页面头部 -->
    <div class="mb-4">
      <a-button @click="handleBack">
        <template #icon>
          <IconRiArrowLeftLine />
        </template>
        返回
      </a-button>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex justify-center items-center py-20">
      <a-spin size="large" />
    </div>

    <!-- 主机详情内容 -->
    <div v-else-if="hostDetail" class="space-y-6">
      <!-- 基本信息 -->
      <a-card title="基本信息" class="rounded-[5px]">
        <a-descriptions
          :data="[
            { label: '主机名称', value: hostDetail.hostname },
            { label: 'IP地址', value: hostDetail.ipAddress },
            { label: 'CPU核心数', value: `${hostDetail.cpu}核` },
            { label: '内存大小', value: `${hostDetail.memory}GB` },
            { label: '操作系统', value: hostDetail.operatingSystem },
            { label: '创建时间', value: hostDetail.createTime },
            { label: '更新时间', value: hostDetail.updateTime }
          ]"
          :column="2"
          layout="horizontal"
          bordered
        />
      </a-card>

      <!-- 硬盘信息 -->
      <a-card title="硬盘配置" class="rounded-[5px]">
        <a-descriptions
          :data="[
            { label: '硬盘总数', value: `${hostDetail.disks.length}块` },
            { label: '总容量', value: `${totalDiskSize}GB` },
            { label: 'SSD数量', value: `${ssdCount}块` },
            { label: 'HDD数量', value: `${hddCount}块` }
          ]"
          :column="2"
          layout="horizontal"
          bordered
        />

        <!-- 硬盘详细列表 -->
        <div class="mt-4">
          <h4 class="text-sm font-medium text-[var(--color-text-2)] mb-3">
            硬盘详情
          </h4>
          <div class="space-y-2">
            <div
              v-for="(disk, index) in hostDetail.disks"
              :key="disk.name"
              class="flex items-center justify-between p-3 bg-[var(--color-fill-2)] rounded"
            >
              <div class="flex items-center gap-3">
                <span class="font-medium">{{ disk.name }}</span>
                <span class="text-sm text-[var(--color-text-3)]"
                  >{{ disk.size }}GB</span
                >
              </div>
              <a-tag
                :color="disk.type === 'ssd' ? 'blue' : 'green'"
                size="small"
              >
                {{ disk.type.toUpperCase() }}
              </a-tag>
            </div>
          </div>
        </div>
      </a-card>

      <!-- 备注信息 -->
      <a-card v-if="hostDetail.remarks" title="备注信息" class="rounded-[5px]">
        <div class="p-4 bg-[var(--color-fill-2)] rounded">
          <p class="text-[var(--color-text-1)] leading-relaxed">
            {{ hostDetail.remarks }}
          </p>
        </div>
      </a-card>

      <!-- 操作按钮 -->
      <div class="flex justify-center gap-4 pt-4">
        <a-button type="primary" @click="handleEdit">
          <template #icon>
            <IconRiEditLine />
          </template>
          编辑主机
        </a-button>
        <a-button @click="handleBack"> 返回列表 </a-button>
      </div>
    </div>

    <!-- 主机不存在 -->
    <div v-else class="flex justify-center items-center py-20">
      <a-empty description="主机不存在" />
    </div>
  </div>
</template>

<style scoped>
.host-detail-page {
  padding: 20px;
}
</style>
