<!--
  用户组管理主页面
  提供用户组的完整管理功能，包括：增删改查、搜索过滤、分页展示、用户关联管理等
  整合了所有子组件，作为用户组管理的入口页面
-->
<script setup lang="ts">
import type { UserGroupRecord, UserGroupListParams } from '@/types/userGroup'
import { getUserGroupListApi, deleteUserGroupApi } from '@/api/userGroup'
import AddUserGroup from './components/AddUserGroup.vue'
import EditUserGroup from './components/EditUserGroup.vue'
import ManageUsers from './components/ManageUsers.vue'

// 搜索表单相关
const searchFormRef = ref() // 搜索表单引用
const searchForm = reactive<Pick<UserGroupListParams, 'name' | 'code'>>({
  name: '', // 用户组名称搜索条件
  code: '' // 用户组编码搜索条件
})

// 表格数据相关
const tableData = ref<UserGroupRecord[]>([]) // 表格数据源
const loading = ref(false) // 表格加载状态

// 弹窗状态管理
const addVisible = ref(false) // 新增用户组弹窗显示状态
const editVisible = ref(false) // 编辑用户组弹窗显示状态
const currentEditUserGroup = ref<UserGroupRecord>() // 当前编辑的用户组数据
const manageUsersVisible = ref(false) // 用户关联管理弹窗显示状态
const manageUserGroupId = ref('') // 当前管理用户关联的用户组ID

// 表格列配置定义
const columns = [
  {
    title: '用户组名称',
    dataIndex: 'name',
    width: 150,
    ellipsis: true, // 超长省略
    tooltip: true // 悬浮提示
  },
  {
    title: '用户组编码',
    dataIndex: 'code',
    width: 150,
    ellipsis: true,
    tooltip: true
  },
  {
    title: '描述',
    dataIndex: 'description',
    width: 200,
    ellipsis: true
  },
  {
    title: '关联用户数',
    dataIndex: 'userIds',
    width: 120,
    slotName: 'userCount' // 使用自定义插槽显示用户数量
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
    ellipsis: true
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 180,
    ellipsis: true
  },
  {
    title: '操作',
    slotName: 'action',
    width: 210,
    fixed: 'right' // 固定在表格右侧
  }
]

// 分页配置
const pagination = reactive({
  current: 1, // 当前页码
  pageSize: 10, // 每页显示条数
  total: 0, // 总记录数
  showTotal: true, // 显示总数信息
  showPageSize: true // 显示每页条数选择器
})

// 执行搜索操作
const handleSearch = () => {
  pagination.current = 1 // 搜索时重置到第一页
  loadTableData()
}

// 重置搜索条件
const handleReset = () => {
  // 清空所有搜索条件
  Object.assign(searchForm, {
    name: '',
    code: ''
  })
  pagination.current = 1 // 重置到第一页
  loadTableData()
}

// 加载表格数据
const loadTableData = async () => {
  loading.value = true
  try {
    // 构建查询参数，包含分页信息
    const params: UserGroupListParams = {
      page: pagination.current,
      pageSize: pagination.pageSize
    }

    // 添加搜索条件（只传递非空值，避免后端接收到空字符串）
    if (searchForm.name?.trim()) {
      params.name = searchForm.name.trim()
    }
    if (searchForm.code?.trim()) {
      params.code = searchForm.code.trim()
    }

    // 调用API获取数据
    const response = await getUserGroupListApi(params)

    // 设置表格数据和分页信息
    if (response.data && response.data.userGroups) {
      tableData.value = response.data.userGroups
      pagination.total = response.data.total || 0
    } else {
      console.error('API响应数据格式不正确:', response)
      tableData.value = []
      pagination.total = 0
    }
  } catch {
    // 发生错误时清空数据，避免显示过期数据
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 顶部操作按钮功能
// 新建用户组
const handleCreate = () => {
  addVisible.value = true
}

// 新增成功回调
const handleAddSuccess = () => {
  loadTableData() // 重新加载数据
}

// 刷新数据
const handleRefresh = () => {
  loadTableData()
}

// 表格操作列功能
// 编辑用户组
const handleEdit = (record: UserGroupRecord) => {
  currentEditUserGroup.value = { ...record }
  editVisible.value = true
}

// 编辑成功回调
const handleEditSuccess = () => {
  loadTableData() // 重新加载数据
}

// 关联用户
const handleManageUsers = (record: UserGroupRecord) => {
  manageUserGroupId.value = record.id!
  manageUsersVisible.value = true
}

// 关联用户成功
const handleManageUsersSuccess = () => {
  manageUsersVisible.value = false
  manageUserGroupId.value = ''
  loadTableData()
}

// 删除用户组
const handleDelete = (record: UserGroupRecord) => {
  // 显示确认对话框
  AModal.confirm({
    title: '确认删除',
    content: `确定要删除用户组 "${record.name}" 吗？删除后不可恢复。`,
    okText: '确定删除',
    cancelText: '取消',
    okButtonProps: { status: 'danger' },
    onOk: async () => {
      await deleteUserGroupApi(record.id!)
      AMessage.success(`用户组 "${record.name}" 删除成功`)
      // 重新加载数据
      loadTableData()
    }
  })
}

// 分页事件处理
// 页码变化
const handlePageChange = (page: number) => {
  pagination.current = page
  loadTableData()
}

// 每页条数变化
const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1 // 重置到第一页
  loadTableData()
}

// 生命周期钩子
// 组件挂载时加载数据
onMounted(() => {
  loadTableData()
})
</script>

<template>
  <a-card class="rounded-[5px]" :bordered="false">
    <!-- 搜索区域 -->
    <a-row>
      <a-col :flex="1">
        <a-form
          ref="searchFormRef"
          :model="searchForm"
          layout="inline"
          label-align="left"
        >
          <a-form-item label="用户组名称" field="name">
            <a-input
              v-model="searchForm.name"
              placeholder="请输入用户组名称"
              :style="{ width: '200px' }"
              allow-clear
            />
          </a-form-item>

          <a-form-item label="用户组编码" field="code">
            <a-input
              v-model="searchForm.code"
              placeholder="请输入用户组编码"
              :style="{ width: '200px' }"
              allow-clear
            />
          </a-form-item>
        </a-form>
      </a-col>

      <a-divider class="h-35px" direction="vertical" />

      <a-col :flex="'86px'" style="text-align: right">
        <a-space :size="18">
          <a-button type="primary" @click="handleSearch">
            <template #icon>
              <IconRiSearchLine />
            </template>
            搜索
          </a-button>
          <a-button @click="handleReset">
            <template #icon>
              <IconRiRefreshLine />
            </template>
            重置
          </a-button>
        </a-space>
      </a-col>
    </a-row>

    <a-divider style="margin-top: 0" />

    <a-row class="mb-4">
      <a-col :span="12">
        <a-space>
          <a-button type="primary" @click="handleCreate">
            <template #icon>
              <IconRiAddLine />
            </template>
            新建
          </a-button>
        </a-space>
      </a-col>
      <a-col :span="12" class="flex items-center justify-end">
        <a-tooltip content="刷新">
          <a-button type="text" @click="handleRefresh">
            <icon-tabler-refresh class="text-[var(--color-neutral-10)]" />
          </a-button>
        </a-tooltip>
      </a-col>
    </a-row>

    <!-- 表格区域 -->
    <div>
      <a-table
        row-key="id"
        :columns="columns"
        :data="tableData"
        :loading="{ loading: loading, tip: '加载中' }"
        :pagination="pagination"
        :bordered="false"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      >
        <template #userCount="{ record }">
          <span> {{ record.userIds?.length || 0 }}</span>
        </template>
        <template #action="{ record }">
          <a-space size="mini">
            <a-button type="text" size="mini" @click="handleEdit(record)">
              编辑
            </a-button>
            <a-divider direction="vertical" :margin="0" />
            <a-button
              type="text"
              size="mini"
              @click="handleManageUsers(record)"
            >
              关联用户
            </a-button>
            <a-divider direction="vertical" :margin="0" />
            <a-button
              type="text"
              size="mini"
              status="danger"
              @click="handleDelete(record)"
            >
              删除
            </a-button>
          </a-space>
        </template>
      </a-table>
    </div>

    <!-- 新增用户组弹窗 -->
    <AddUserGroup v-model:visible="addVisible" @success="handleAddSuccess" />

    <!-- 编辑用户组弹窗 -->
    <EditUserGroup
      v-model:visible="editVisible"
      :user-group-data="currentEditUserGroup"
      @success="handleEditSuccess"
    />

    <!-- 关联用户组件 -->
    <ManageUsers
      v-model:visible="manageUsersVisible"
      :user-group-id="manageUserGroupId"
      @success="handleManageUsersSuccess"
    />
  </a-card>
</template>

<style scoped></style>
