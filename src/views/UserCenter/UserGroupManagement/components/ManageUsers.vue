<script setup lang="ts">
/**
 * 用户组关联用户管理组件
 *
 * 功能说明：
 * - 使用穿梭框组件实现用户与用户组的关联管理
 * - 支持用户搜索、批量选择、拖拽等操作
 * - 智能变化检测，只有在有实际变更时才允许提交
 * - 独立弹窗组件，使用v-model:visible控制显示状态
 * - 支持用户状态判断（禁用用户不可选择）
 */

import type { UserRecord } from '@/types/user'
import { getUserListApi } from '@/api/user'
import { getUserGroupInfoApi, updateUserGroupApi } from '@/api/userGroup'
import type { UpdateUserGroupRequest } from '@/types/userGroup'

/**
 * 组件属性接口
 */
interface Props {
  /** 弹窗显示状态 */
  visible: boolean
  /** 用户组ID */
  userGroupId: string
}

/**
 * 组件事件接口
 */
interface Emits {
  /** 更新弹窗显示状态 */
  (e: 'update:visible', visible: boolean): void
  /** 操作成功事件 */
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

/**
 * 响应式数据定义
 */

/** 穿梭框数据源 - 包含所有可选用户的列表 */
const transferDataSource = ref<
  Array<{ value: string; label: string; disabled?: boolean }>
>([])

/** 原始完整的用户数据，用于搜索过滤 */
const originalUserDataSource = ref<
  Array<{ value: string; label: string; disabled?: boolean }>
>([])

/** 当前已选中的用户ID列表（穿梭框右侧） */
const selectedUserIds = ref<string[]>([])

/** 原始完整的已选中用户ID列表，用于搜索过滤和变化检测 */
const originalSelectedUserIds = ref<string[]>([]) // 既用于搜索过滤，也用于变化检测

/** 数据加载状态 */
const isLoading = ref(false)

/** 提交操作加载状态 */
const isSubmitting = ref(false)

/**
 * 检测用户关联关系是否发生变化
 * 通过对比当前选中的用户ID和初始用户ID来判断
 */
const hasChanges = computed(() => {
  return !areArraysEqualUnordered(
    selectedUserIds.value,
    originalSelectedUserIds.value
  )
})

/**
 * 加载用户数据和用户组关联信息
 * 并行请求用户列表和用户组详情，转换数据格式用于穿梭框显示
 */
const loadUserData = async () => {
  if (!props.userGroupId) return

  isLoading.value = true
  try {
    // 并行获取用户列表和用户组信息，提高加载效率
    const [usersResponse, userGroupResponse] = await Promise.all([
      getUserListApi({ pageSize: 10, page: 1 }),
      getUserGroupInfoApi(props.userGroupId)
    ])

    // 转换用户数据为穿梭框所需的格式
    if (usersResponse.data?.users) {
      const userData = usersResponse.data.users
        .filter((user: UserRecord) => user.id) // 过滤掉没有ID的异常用户
        .map((user: UserRecord) => ({
          value: user.id!,
          label: `${user.displayName} (${user.username})`, // 显示名称和用户名
          disabled: user.status === 'disable' // 禁用状态的用户不允许选择
        }))

      // 保存原始完整数据用于搜索过滤
      originalUserDataSource.value = [...userData]
      transferDataSource.value = [...userData]
    }

    // 设置已关联的用户ID列表（穿梭框右侧）
    const userIds = userGroupResponse.data?.userIds || []
    originalSelectedUserIds.value = [...userIds] // 保存原始完整数据，既用于搜索过滤，也用于变化检测
    selectedUserIds.value = [...userIds]
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    isLoading.value = false
  }
}

/**
 * 数组无序比较工具函数
 * 比较两个字符串数组是否包含相同的元素（忽略顺序）
 * @param a 第一个数组
 * @param b 第二个数组
 * @returns 是否相等
 */
const areArraysEqualUnordered = (a: string[], b: string[]) => {
  // 长度不同直接返回false
  if (a.length !== b.length) {
    return false
  }

  // 创建副本并排序，避免修改原数组
  const sortedA = [...a].sort()
  const sortedB = [...b].sort()

  // 比较排序后的数组是否完全一致
  return JSON.stringify(sortedA) === JSON.stringify(sortedB)
}

/**
 * 提交用户关联关系更新
 * 检查变化后调用API更新用户组的用户关联关系
 */
const handleUserGroupUpdate = async () => {
  // 如果没有变化，提示用户并阻止提交
  if (!hasChanges.value) {
    AMessage.info('用户关联关系未发生变化')
    return
  }

  isSubmitting.value = true
  try {
    // 构造更新请求数据，只更新用户关联关系
    const updateData: UpdateUserGroupRequest = {
      userIds: selectedUserIds.value
    }
    await updateUserGroupApi(props.userGroupId, updateData)

    AMessage.success('用户关联更新成功')
    emit('update:visible', false) // 关闭弹窗
    emit('success') // 通知父组件刷新数据
  } catch (error) {
    console.error('更新用户关联失败:', error)
  } finally {
    isSubmitting.value = false
  }
}

/**
 * 取消操作处理
 * 直接关闭弹窗，不保存任何更改
 */
const handleCancel = () => {
  emit('update:visible', false)
}

/**
 * 处理穿梭框搜索事件
 * @param value 搜索关键词
 * @param type 搜索类型 - 'source' 或 'target'
 */
const handleTransferSearch = (value: string, type: 'source' | 'target') => {
  if (type === 'source') {
    // 搜索左侧数据源（可选用户列表）
    if (!value.trim()) {
      // 没有搜索关键词时显示所有数据
      transferDataSource.value = [...originalUserDataSource.value]
    } else {
      // 根据用户名和显示名称进行模糊搜索
      transferDataSource.value = originalUserDataSource.value.filter(item =>
        item.label.toLowerCase().includes(value.toLowerCase())
      )
    }
  } else if (type === 'target') {
    // 搜索右侧目标列表（已选中用户列表）
    if (!value.trim()) {
      // 没有搜索关键词时显示所有已选中用户
      selectedUserIds.value = [...originalSelectedUserIds.value]
    } else {
      // 根据已选中用户进行过滤
      const filteredUsers = originalUserDataSource.value.filter(
        item =>
          originalSelectedUserIds.value.includes(item.value) &&
          item.label.toLowerCase().includes(value.toLowerCase())
      )
      selectedUserIds.value = filteredUsers.map(item => item.value)
    }
  }
}

/**
 * 监听弹窗显示状态
 * 当弹窗打开且有用户组ID时，自动加载数据
 */
watch(
  () => props.visible,
  visible => {
    if (visible && props.userGroupId) {
      loadUserData()
    }
  }
)
</script>

<template>
  <a-modal
    :visible="visible"
    title="关联用户"
    :footer="false"
    width="650px"
    @update:visible="emit('update:visible', $event)"
    @cancel="handleCancel"
  >
    <div v-if="isLoading" class="flex items-center justify-center py-16">
      <a-spin tip="加载用户数据中..." />
    </div>

    <div v-else>
      <!-- 友好提示信息 -->
      <a-alert
        type="info"
        show-icon
        class="mb-6"
        :closable="false"
        message="操作说明"
      >
        <div class="text-sm text-[var(--color-text-2)]">
          • 左侧为可选用户列表，右侧为已关联用户列表<br />
          • 使用中间的箭头按钮或拖拽来移动用户<br />
          • 支持搜索功能快速查找用户<br />
          • 状态为"禁用"的用户无法选择
        </div>
      </a-alert>

      <!-- 穿梭框组件 -->
      <a-transfer
        v-model="selectedUserIds"
        show-search
        :data="transferDataSource"
        :source-input-search-props="{
          placeholder: '输入用户名搜索用户'
        }"
        :target-input-search-props="{
          placeholder: '输入用户名搜索用户'
        }"
        @search="handleTransferSearch"
        class="custom-transfer"
      >
        <template
          #source-title="{
            countTotal,
            countSelected,
            checked,
            indeterminate,
            onSelectAllChange
          }"
        >
          <div class="flex items-center justify-between pr-8px">
            可选用户 {{ countSelected }}-{{ countTotal }}
            <a-checkbox
              :model-value="checked"
              :indeterminate="indeterminate"
              @change="onSelectAllChange"
            />
          </div>
        </template>

        <template
          #target-title="{
            countTotal,
            countSelected,
            checked,
            indeterminate,
            onSelectAllChange
          }"
        >
          <div class="flex items-center justify-between pr-8px">
            目标用户 {{ countSelected }}-{{ countTotal }}
            <a-checkbox
              :model-value="checked"
              :indeterminate="indeterminate"
              @change="onSelectAllChange"
            />
          </div>
        </template>
      </a-transfer>
      <!-- 操作按钮 -->
      <div class="flex justify-end mt-6">
        <a-space>
          <a-button size="large" @click="handleCancel"> 取消 </a-button>
          <a-button
            type="primary"
            size="large"
            :loading="isSubmitting"
            @click="handleUserGroupUpdate"
            :disabled="!hasChanges || transferDataSource.length === 0"
          >
            更新
          </a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<style scoped>
.custom-transfer :deep(.arco-transfer-view) {
  width: 100%;
  height: 400px;
}
</style>
