<!--
  新增用户组弹窗组件
  用于创建新用户组的模态框界面，内部使用UserGroupForm组件处理表单逻辑
  提供加载状态显示和完整的错误处理机制
-->
<script setup lang="ts">
import type { UserGroupRecord, CreateUserGroupRequest } from '@/types/userGroup'
import { createUserGroupApi } from '@/api/userGroup'
import UserGroupForm from './UserGroupForm.vue'

// 组件事件定义 - 通知父组件操作成功
const emit = defineEmits<{
  success: []
}>()

// 响应式数据定义
const visible = defineModel<boolean>('visible', { default: false }) // 弹窗显示状态
const loading = ref(false) // 加载状态，用于API请求期间的UI反馈
const formRef = ref() // 子表单组件引用

/**
 * 处理表单提交事件
 * 调用API创建用户组，成功后关闭弹窗并通知父组件
 * @param formData 表单数据（UserGroupRecord格式）
 */
const handleSubmit = async (formData: UserGroupRecord) => {
  loading.value = true
  try {
    // 转换为创建请求参数类型，过滤掉不需要的字段
    const createRequest: CreateUserGroupRequest = {
      name: formData.name,
      code: formData.code,
      description: formData.description,
      userIds: formData.userIds
    }

    await createUserGroupApi(createRequest)
    AMessage.success('用户组新增成功')
    visible.value = false
    emit('success') // 通知父组件刷新数据
  } catch (error) {
    console.error('创建用户组失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 处理取消操作
 * 直接关闭弹窗，表单重置由watch监听器处理
 */
const handleCancel = () => {
  visible.value = false
}

/**
 * 监听弹窗关闭事件
 * 当弹窗关闭时，重置表单状态以确保下次打开时是干净状态
 */
watch(visible, newVal => {
  if (!newVal) {
    nextTick(() => {
      formRef.value?.resetForm()
    })
  }
})
</script>

<template>
  <!--
    新增用户组弹窗组件 - 用于创建新用户组
    使用模态框形式展示，内部嵌套UserGroupForm组件处理表单逻辑
    支持加载状态显示和错误处理
  -->
  <a-modal
    v-model:visible="visible"
    title="新增用户组"
    width="550px"
    :footer="false"
    :mask-closable="false"
    @cancel="handleCancel"
  >
    <!-- 加载遮罩层 - 在API请求期间显示 -->
    <a-spin :loading="loading" style="width: 100%">
      <!--
        用户组表单组件 - 设置为新增模式
        @submit: 接收表单提交事件，触发handleSubmit方法
        @cancel: 接收取消事件，触发handleCancel方法
      -->
      <UserGroupForm
        ref="formRef"
        mode="add"
        @submit="handleSubmit"
        @cancel="handleCancel"
      />
    </a-spin>
  </a-modal>
</template>

<style scoped></style>
