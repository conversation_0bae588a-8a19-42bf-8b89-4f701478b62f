<!--
  编辑用户组弹窗组件
  用于编辑现有用户组的模态框界面，内部使用UserGroupForm组件处理表单逻辑
  接受用户组数据作为props，提供完整的编辑功能和错误处理
-->
<script setup lang="ts">
import type { UserGroupRecord } from '@/types/userGroup'
import { updateUserGroupApi } from '@/api/userGroup'
import UserGroupForm from './UserGroupForm.vue'

// 组件Props定义
interface Props {
  userGroupData?: UserGroupRecord // 要编辑的用户组数据
}

const props = defineProps<Props>()
// 计算属性：当前编辑的用户组数据，用于表单初始化
const currentUserGroup = computed(() => props.userGroupData)

// 组件事件定义 - 通知父组件操作成功
const emit = defineEmits<{
  success: []
}>()

// 响应式数据定义
const visible = defineModel<boolean>('visible', { default: false }) // 弹窗显示状态
const loading = ref(false) // 加载状态，用于API请求期间的UI反馈

/**
 * 处理表单提交事件
 * 调用API更新用户组信息，成功后关闭弹窗并通知父组件
 * @param formData 表单数据（UserGroupRecord格式）
 */
const handleSubmit = async (formData: UserGroupRecord) => {
  loading.value = true
  try {
    // 调用更新API，只传递基础信息字段（不包含用户关联）
    await updateUserGroupApi(formData.id!, {
      name: formData.name,
      code: formData.code,
      description: formData.description
    })

    AMessage.success('用户组编辑成功')
    visible.value = false
    emit('success') // 通知父组件刷新数据
  } catch (error) {
    console.error('更新用户组失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 处理取消操作
 * 直接关闭弹窗，表单会自动恢复到初始状态
 */
const handleCancel = () => {
  visible.value = false
}
</script>

<template>
  <!--
    编辑用户组弹窗组件 - 用于编辑现有用户组
    使用模态框形式展示，内部嵌套UserGroupForm组件处理表单逻辑
    支持加载状态显示和错误处理
  -->
  <a-modal
    v-model:visible="visible"
    title="编辑用户组"
    width="550px"
    :footer="false"
    :mask-closable="false"
    @cancel="handleCancel"
  >
    <!-- 加载遮罩层 - 在API请求期间显示 -->
    <a-spin :loading="loading" style="width: 100%">
      <!--
        用户组表单组件 - 设置为编辑模式
        :initial-data: 传递初始数据给表单组件
        @submit: 接收表单提交事件，触发handleSubmit方法
        @cancel: 接收取消事件，触发handleCancel方法
      -->
      <UserGroupForm
        mode="edit"
        :initial-data="currentUserGroup"
        @submit="handleSubmit"
        @cancel="handleCancel"
      />
    </a-spin>
  </a-modal>
</template>

<style scoped></style>
