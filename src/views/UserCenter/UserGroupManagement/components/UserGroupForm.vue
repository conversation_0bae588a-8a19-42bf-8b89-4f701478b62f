<!--
  用户组表单组件
  可复用的表单组件，同时支持新增和编辑模式
  提供完整的表单验证和数据处理逻辑
-->
<script setup lang="ts">
import type { UserGroupRecord } from '@/types/userGroup'

// 组件Props定义
interface Props {
  mode: 'add' | 'edit' // 表单模式：新增或编辑
  initialData?: UserGroupRecord // 初始数据（编辑模式使用）
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'add'
})

// 组件事件定义
const emit = defineEmits<{
  submit: [formData: UserGroupRecord] // 表单提交事件
  cancel: [] // 取消操作事件
}>()

// 响应式数据定义
const formRef = ref() // 表单引用
// 表单数据模型，初始化为空值
const formData = reactive<UserGroupRecord>({
  name: '',
  code: '',
  description: '',
  userIds: []
})

/**
 * 表单验证规则
 * 根据业务需求定义各字段的验证规则
 */
const formRules = computed(() => ({
  name: [
    { required: true, message: '请输入用户组名称' },
    { minLength: 2, maxLength: 50, message: '用户组名称长度为2-50个字符' }
  ],
  code: [
    { required: true, message: '请输入用户组编码' },
    {
      match: /^[a-zA-Z][a-zA-Z0-9_]*$/,
      message: '编码必须以字母开头，只能包含字母、数字和下划线'
    },
    { minLength: 2, maxLength: 30, message: '用户组编码长度为2-30个字符' }
  ],
  description: [{ maxLength: 200, message: '描述不能超过200个字符' }]
}))

/**
 * 监听初始数据变化
 * 当父组件传入初始数据时，自动填充表单
 */
watch(
  () => props.initialData,
  newValue => {
    if (newValue) {
      Object.assign(formData, newValue)
    }
  },
  { immediate: true, deep: true }
)

/**
 * 提交表单处理
 * 先进行表单验证，验证通过后触发submit事件
 */
const handleSubmit = async () => {
  try {
    const validateResult = await formRef.value?.validate()
    if (validateResult) {
      return // 验证失败，停止提交
    }
    emit('submit', { ...formData }) // 提交表单数据副本
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

/**
 * 取消操作处理
 * 直接触发cancel事件，由父组件处理后续逻辑
 */
const handleCancel = () => {
  emit('cancel')
}

/**
 * 重置表单状态
 * 清空表单数据并重置验证状态
 */
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(formData, {
    name: '',
    code: '',
    description: '',
    userIds: []
  })
}

// 暴露方法给父组件使用
defineExpose({
  resetForm,
  validate: () => formRef.value?.validate()
})
</script>

<template>
  <a-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-align="right"
    auto-label-width
  >
    <!-- 第一行：用户组名称 -->
    <a-form-item label="用户组名称" field="name">
      <a-input
        v-model="formData.name"
        placeholder="请输入用户组名称"
        :max-length="50"
        show-word-limit
      />
    </a-form-item>

    <!-- 第二行：用户组编码 -->
    <a-form-item label="用户组编码" field="code">
      <a-input
        v-model="formData.code"
        placeholder="请输入用户组编码"
        :max-length="30"
        show-word-limit
        :disabled="mode === 'edit'"
      />
      <template #extra>
        <div class="text-xs text-gray-500">
          编码必须以字母开头，只能包含字母、数字和下划线
          <span v-if="mode === 'edit'" class="text-orange-500"
            >（编辑时不可修改）</span
          >
        </div>
      </template>
    </a-form-item>

    <!-- 第三行：用户组描述 -->
    <a-form-item label="用户组描述" field="description">
      <a-textarea
        v-model="formData.description"
        placeholder="请输入用户组描述（可选）"
        :max-length="200"
        :rows="3"
        show-word-limit
      />
    </a-form-item>

    <!-- 操作按钮 -->
    <div class="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200">
      <a-button @click="handleCancel"> 取消 </a-button>
      <a-button type="primary" @click="handleSubmit">
        {{ mode === 'edit' ? '更新用户组' : '创建用户组' }}
      </a-button>
    </div>
  </a-form>
</template>

<style scoped>
:deep(.arco-form-item-extra) {
  margin-top: 4px;
}
</style>
