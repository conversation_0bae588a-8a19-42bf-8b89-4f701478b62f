<script setup lang="ts">
import type { UserRecord } from '@/types/user'
import { updateUserApi } from '@/api/user'
import UserForm from './UserForm.vue'

/**
 * 组件属性定义
 */
interface Props {
  /** 要编辑的用户数据 - 来自父组件传递的用户信息 */
  userData?: UserRecord
}

const props = defineProps<Props>()

/**
 * 组件事件定义
 */
const emit = defineEmits<{
  /** 编辑成功事件 - 当用户信息成功更新后触发，通知父组件刷新数据 */
  success: []
}>()

/**
 * 弹窗显示状态 - 使用v-model双向绑定控制弹窗的显示和隐藏
 * 父组件可以通过v-model:visible来控制弹窗状态
 */
const visible = defineModel<boolean>('visible', { default: false })

/**
 * 加载状态 - 控制提交按钮和表单的loading状态
 * 在API请求期间防止用户重复提交
 */
const loading = ref(false)

/**
 * 计算初始数据 - 将UserRecord转换为表单可用的格式
 * 过滤掉不需要在表单中编辑的字段（如id、createTime等）
 */
const initialData = computed(() => {
  if (!props.userData) return {}

  return {
    displayName: props.userData.displayName,
    username: props.userData.username,
    email: props.userData.email || '',
    phone: props.userData.phone || '',
    authType: props.userData.authType,
    status: props.userData.status,
    idCard: props.userData.idCard || '',
    groupIds: props.userData.groupIds || []
  }
})

/**
 * 处理表单提交 - 编辑用户的核心逻辑
 * @param formData - 从子组件UserForm传递上来的表单数据
 */
const handleSubmit = async (formData: UserRecord) => {
  // 验证是否有用户ID - 编辑操作必需的参数
  if (!props.userData?.id) {
    AMessage.error('用户ID不存在')
    return
  }

  loading.value = true

  try {
    // 调用API更新用户信息
    await updateUserApi(props.userData.id, formData)

    // 显示成功提示
    AMessage.success('用户信息更新成功')

    // 关闭弹窗
    visible.value = false

    // 触发成功事件，通知父组件刷新数据
    emit('success')
  } catch (error) {
    // 显示错误提示
    AMessage.error('用户信息更新失败，请重试')
    console.error('更新用户失败:', error)
  } finally {
    // 无论成功还是失败都要取消加载状态
    loading.value = false
  }
}

/**
 * 处理取消操作 - 关闭弹窗
 * 当用户点击取消按钮或弹窗的X按钮时触发
 */
const handleCancel = () => {
  visible.value = false
}
</script>

<template>
  <!-- 
    编辑用户弹窗组件 - 用于修改现有用户信息
    使用模态框形式展示，内部嵌套UserForm组件处理表单逻辑
    支持预填充用户数据、加载状态显示和错误处理
  -->
  <a-modal
    v-model:visible="visible"
    title="编辑用户"
    width="550px"
    :footer="false"
    :mask-closable="false"
    @cancel="handleCancel"
  >
    <!-- 加载遮罩层 - 在API请求期间显示 -->
    <a-spin :loading="loading" style="width: 100%">
      <!-- 
        用户表单组件 - 设置为编辑模式
        mode="edit": 编辑模式，密码字段变为可选
        :initial-data: 传入初始数据用于表单预填充
        @submit: 接收表单提交事件，触发handleSubmit方法
        @cancel: 接收取消事件，触发handleCancel方法
      -->
      <UserForm
        mode="edit"
        :initial-data="initialData"
        @submit="handleSubmit"
        @cancel="handleCancel"
      />
    </a-spin>
  </a-modal>
</template>

<style scoped></style>
