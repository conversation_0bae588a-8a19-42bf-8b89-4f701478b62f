<script setup lang="ts">
import type { UserRecord } from '@/types/user'
import { createUserApi } from '@/api/user'
import UserForm from './UserForm.vue'

/**
 * 组件事件定义
 */
const emit = defineEmits<{
  /** 新增成功事件 - 当用户成功创建后触发，通知父组件刷新数据 */
  success: []
}>()

/**
 * 弹窗显示状态 - 使用v-model双向绑定控制弹窗的显示和隐藏
 * 父组件可以通过v-model:visible来控制弹窗状态
 */
const visible = defineModel<boolean>('visible', { default: false })

/**
 * 加载状态 - 控制提交按钮和表单的loading状态
 * 在API请求期间防止用户重复提交
 */
const loading = ref(false)

/**
 * 处理表单提交 - 新增用户的核心逻辑
 * @param formData - 从子组件UserForm传递上来的表单数据
 */
const handleSubmit = async (formData: UserRecord) => {
  loading.value = true

  try {
    // 调用API创建用户
    await createUserApi(formData)

    // 显示成功提示
    AMessage.success('用户新增成功')

    // 关闭弹窗
    visible.value = false

    // 触发成功事件，通知父组件刷新数据
    emit('success')
  } finally {
    // 无论成功还是失败都要取消加载状态
    loading.value = false
  }
}

/**
 * 处理取消操作 - 关闭弹窗
 * 当用户点击取消按钮或弹窗的X按钮时触发
 */
const handleCancel = () => {
  visible.value = false
}
</script>

<template>
  <!-- 
    新增用户弹窗组件 - 用于创建新用户
    使用模态框形式展示，内部嵌套UserForm组件处理表单逻辑
    支持加载状态显示和错误处理
  -->
  <a-modal
    v-model:visible="visible"
    title="新增用户"
    width="550px"
    :footer="false"
    :mask-closable="false"
    @cancel="handleCancel"
  >
    <!-- 加载遮罩层 - 在API请求期间显示 -->
    <a-spin :loading="loading" style="width: 100%">
      <!-- 
        用户表单组件 - 设置为新增模式
        @submit: 接收表单提交事件，触发handleSubmit方法
        @cancel: 接收取消事件，触发handleCancel方法
      -->
      <UserForm mode="add" @submit="handleSubmit" @cancel="handleCancel" />
    </a-spin>
  </a-modal>
</template>

<style scoped></style>
