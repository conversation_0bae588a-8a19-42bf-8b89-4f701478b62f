<script setup lang="ts">
import { getUserGroupListApi } from '@/api/userGroup'
import type { UserRecord, UserAuthType, UserStatus } from '@/types/user'
import type { UserGroupRecord } from '@/types/userGroup'

/**
 * 组件属性接口定义
 */
interface Props {
  /** 表单模式：add-新增用户，edit-编辑用户 */
  mode?: 'add' | 'edit'
  /** 初始数据，编辑模式下用于填充表单 */
  initialData?: Partial<UserRecord>
}

/**
 * 组件事件定义
 */
const emit = defineEmits<{
  /** 表单提交事件 - 当用户点击提交按钮且验证通过时触发 */
  submit: [data: UserRecord]
  /** 取消操作事件 - 当用户点击取消按钮时触发 */
  cancel: []
}>()

// 使用默认值设置组件属性
const props = withDefaults(defineProps<Props>(), {
  mode: 'add',
  initialData: () => ({})
})

// 表单引用，用于调用表单验证方法
const formRef = ref()

// 表单数据 - 响应式数据，与表单输入双向绑定
const formData = reactive<UserRecord>({
  displayName: '',
  username: '',
  email: '',
  phone: '',
  authType: 'local',
  status: 'enable',
  idCard: '',
  password: '',
  confirmPassword: '',
  groupIds: []
})

// 用户组选项数据
const userGroupOptions = ref<UserGroupRecord[]>([])
const userGroupLoading = ref(false)

/**
 * 表单验证规则配置
 * 根据业务需求定义各字段的验证规则
 */
const rules = computed(() => ({
  // 显示名称验证：必填，长度限制
  displayName: [
    { required: true, message: '请输入姓名' },
    { minLength: 2, maxLength: 30, message: '姓名长度为2-30个字符' }
  ],
  // 登录名验证：必填，格式限制（只能包含字母、数字、下划线）
  username: [
    { required: true, message: '请输入用户名' },
    {
      match: /^[a-zA-Z0-9_]{3,30}$/,
      message: '用户名只能包含字母、数字、下划线，长度为3-30个字符'
    }
  ],
  // 邮箱验证：可选，但输入时必须符合邮箱格式
  email: [
    {
      type: 'email',
      message: '请输入正确的邮箱格式'
    }
  ],
  // 手机号验证：可选，但输入时必须符合中国大陆手机号格式
  phone: [
    {
      match: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号格式'
    }
  ],
  // 认证方式验证：必填
  authType: [{ required: true, message: '请选择认证方式' }],
  // 用户状态验证：必填
  status: [{ required: true, message: '请选择用户状态' }],
  // 身份证号验证：可选，但输入时必须符合18位身份证格式
  idCard: [
    {
      match:
        /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
      message: '请输入正确的身份证号格式'
    }
  ],
  // 密码验证：仅在本地认证时需要
  password: [
    // 新增模式 + 本地认证：密码必填
    ...(props.mode === 'add' && formData.authType === 'local'
      ? [{ required: true, message: '请输入密码' }]
      : []),
    // 编辑模式 + 本地认证 + 有输入：验证长度
    ...(formData.authType === 'local'
      ? [{ minLength: 6, maxLength: 20, message: '密码长度为6-20个字符' }]
      : [])
  ],
  // 确认密码验证：仅在本地认证时需要
  confirmPassword: [
    // 新增模式 + 本地认证：确认密码必填
    ...(props.mode === 'add' && formData.authType === 'local'
      ? [{ required: true, message: '请确认密码' }]
      : []),
    // 本地认证时：验证一致性
    ...(formData.authType === 'local'
      ? [
          {
            validator: (value: string, callback: (error?: string) => void) => {
              // 如果密码字段为空，则不验证一致性
              if (!formData.password) {
                callback()
                return
              }
              // 如果确认密码为空且密码不为空，则报错
              if (!value && formData.password) {
                callback('请确认密码')
                return
              }
              // 检查两次输入是否一致
              if (value !== formData.password) {
                callback('两次输入的密码不一致')
                return
              }
              callback()
            }
          }
        ]
      : [])
  ]
}))

/**
 * 计算是否显示密码字段
 * 只有在本地认证时才显示密码相关字段
 */
const showPasswordFields = computed(() => {
  return formData.authType === 'local'
})

/**
 * 监听认证方式变化，清空密码字段
 * 当从本地认证切换到LDAP认证时，清空密码相关字段
 */
watch(
  () => formData.authType,
  newAuthType => {
    if (newAuthType === 'ldap') {
      formData.password = ''
      formData.confirmPassword = ''
      // 清除密码字段的验证错误
      formRef.value?.clearValidate(['password', 'confirmPassword'])
    }
  }
)

/**
 * 监听密码变化，重新验证确认密码
 * 当密码字段变化时，重新验证确认密码字段的一致性
 */
watch(
  () => formData.password,
  () => {
    // 如果确认密码不为空，重新验证确认密码字段
    if (formData.confirmPassword) {
      nextTick(() => {
        formRef.value?.validateField('confirmPassword')
      })
    }
  }
)

/**
 * 获取用户组列表
 * 用于填充用户组选择器的选项数据
 */
const fetchUserGroups = async () => {
  try {
    userGroupLoading.value = true
    const response = await getUserGroupListApi({ page: 1, pageSize: 1000 })
    if (response.code === 200) {
      userGroupOptions.value = response.data.userGroups
    }
  } catch (error) {
    console.error('获取用户组列表失败:', error)
  } finally {
    userGroupLoading.value = false
  }
}

/**
 * 初始化表单数据
 * 根据传入的初始数据填充表单，主要用于编辑模式
 */
const initFormData = () => {
  Object.assign(formData, {
    displayName: '',
    username: '',
    email: '',
    phone: '',
    authType: 'local' as UserAuthType,
    status: 'enable' as UserStatus,
    idCard: '',
    password: '',
    confirmPassword: '',
    groupIds: [],
    ...props.initialData
  })
}

/**
 * 处理表单提交
 * 执行表单验证，验证通过后触发submit事件
 */
const handleSubmit = async () => {
  try {
    // 执行表单验证
    const validateResult = await formRef.value?.validate()
    if (validateResult) {
      // 有验证错误，不提交
      return
    }

    // 表单验证通过，处理提交逻辑
    // 克隆表单数据，避免直接修改原始数据
    const submitData = { ...formData }

    // 处理密码字段逻辑
    if (formData.authType === 'ldap') {
      // LDAP认证：移除密码相关字段
      delete submitData.password
      delete submitData.confirmPassword
    } else if (props.mode === 'edit' && !submitData.password) {
      // 编辑模式 + 本地认证 + 密码为空：表示不修改密码
      delete submitData.password
      delete submitData.confirmPassword
    }

    // 始终移除确认密码字段（这个字段只用于前端验证）
    delete submitData.confirmPassword

    // 触发提交事件，将数据传递给父组件
    emit('submit', submitData)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

/**
 * 重置表单
 * 清空所有表单数据并重置验证状态
 */
const resetForm = () => {
  formRef.value?.resetFields()
  initFormData()
}

/**
 * 处理取消操作
 * 重置表单并触发cancel事件，通知父组件用户取消操作
 */
const handleCancel = () => {
  resetForm()
  emit('cancel')
}

// 组件挂载时获取用户组列表
onMounted(() => {
  fetchUserGroups()
})

// 监听初始数据变化，当编辑的用户数据变化时重新初始化表单
watch(() => props.initialData, initFormData, { immediate: true, deep: true })

/**
 * 暴露组件方法供父组件调用
 * resetForm: 重置表单方法
 * validate: 表单验证方法
 */
defineExpose({
  resetForm,
  validate: () => formRef.value?.validate()
})
</script>

<template>
  <!-- 
    用户表单组件 - 封装用户新增和编辑的表单逻辑
    支持新增模式（密码必填）和编辑模式（密码可选）
    集成完整的表单验证和数据处理
  -->
  <a-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    :label-col-props="{ span: 6 }"
    :wrapper-col-props="{ span: 18 }"
    label-align="right"
    auto-label-width
  >
    <!-- 用户名 -->
    <a-row>
      <a-col :span="24">
        <a-form-item label="用户名" field="username">
          <a-input
            v-model="formData.username"
            placeholder="请输入用户名"
            allow-clear
          />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 姓名 -->
    <a-row>
      <a-col :span="24">
        <a-form-item label="姓名" field="displayName">
          <a-input
            v-model="formData.displayName"
            placeholder="请输入姓名"
            allow-clear
          />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 邮箱 -->
    <a-row>
      <a-col :span="24">
        <a-form-item label="邮箱" field="email">
          <a-input
            v-model="formData.email"
            placeholder="请输入邮箱"
            allow-clear
          />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 手机号 -->
    <a-row>
      <a-col :span="24">
        <a-form-item label="手机号" field="phone">
          <a-input
            v-model="formData.phone"
            placeholder="请输入手机号"
            allow-clear
          />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 认证方式 -->
    <a-row>
      <a-col :span="24">
        <a-form-item label="认证方式" field="authType">
          <a-select v-model="formData.authType" placeholder="请选择认证方式">
            <a-option value="local">本地认证</a-option>
            <a-option value="ldap">LDAP</a-option>
          </a-select>
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 用户状态 -->
    <a-row>
      <a-col :span="24">
        <a-form-item label="用户状态" field="status">
          <a-select v-model="formData.status" placeholder="请选择用户状态">
            <a-option value="enable">启用</a-option>
            <a-option value="disable">禁用</a-option>
          </a-select>
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 身份证号 -->
    <a-row>
      <a-col :span="24">
        <a-form-item label="身份证号" field="idCard">
          <a-input
            v-model="formData.idCard"
            placeholder="请输入身份证号"
            allow-clear
          />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 用户组 -->
    <a-row>
      <a-col :span="24">
        <a-form-item label="用户组" field="groupIds">
          <a-select
            v-model="formData.groupIds"
            :options="userGroupOptions"
            :field-names="{ value: 'id', label: 'name' }"
            :loading="userGroupLoading"
            placeholder="请选择用户组"
            multiple
            allow-clear
            allow-search
          >
            <template #option="{ data }">
              <div>
                <div>{{ data.name }}</div>
                <div class="text-xs text-gray-500">{{ data.code }}</div>
              </div>
            </template>
          </a-select>
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 密码相关字段 - 仅在本地认证时显示 -->
    <template v-if="showPasswordFields">
      <!-- 密码 -->
      <a-row>
        <a-col :span="24">
          <a-form-item
            :label="mode === 'add' ? '密码' : '新密码'"
            field="password"
          >
            <a-input-password
              v-model="formData.password"
              :placeholder="
                mode === 'add' ? '请输入密码' : '留空表示不修改密码'
              "
              allow-clear
            />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 确认密码 -->
      <a-row>
        <a-col :span="24">
          <a-form-item
            :label="mode === 'add' ? '确认密码' : '确认新密码'"
            field="confirmPassword"
          >
            <a-input-password
              v-model="formData.confirmPassword"
              :placeholder="mode === 'add' ? '请再次输入密码' : '请确认新密码'"
              allow-clear
            />
          </a-form-item>
        </a-col>
      </a-row>
    </template>

    <!-- 操作按钮行 -->
    <a-row>
      <a-col :span="24" class="text-right">
        <a-space>
          <!-- 取消按钮 -->
          <a-button @click="handleCancel">取消</a-button>
          <!-- 提交按钮 - 根据模式显示不同文本 -->
          <a-button type="primary" @click="handleSubmit">
            {{ mode === 'add' ? '新增' : '保存' }}
          </a-button>
        </a-space>
      </a-col>
    </a-row>
  </a-form>
</template>

<style scoped></style>
