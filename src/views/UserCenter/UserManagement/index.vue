<script setup lang="ts">
import { getUserGroupListApi } from '@/api/userGroup'
import type { UserRecord, UserAuthType, UserStatus } from '@/types/user'
import type { UserGroupRecord } from '@/types/userGroup'
import {
  getUserListApi,
  deleteUser<PERSON>pi,
  enableUser<PERSON>pi,
  disableUser<PERSON><PERSON>,
  type UserListParams
} from '@/api/user'
import AddUser from './components/AddUser.vue'
import EditUser from './components/EditUser.vue'

/**
 * 搜索表单相关
 */
// 搜索表单引用
const searchFormRef = ref()
// 搜索表单数据
const searchForm = reactive({
  displayName: '', // 姓名
  username: '', // 用户名
  groupId: '', // 用户组ID
  phone: '', // 手机号
  authType: undefined as UserAuthType | undefined, // 认证方式
  status: undefined as UserStatus | undefined // 用户状态
})

/**
 * 表格相关
 */
// 表格数据
const tableData = ref<UserRecord[]>([])
// 加载状态
const loading = ref(false)
// 用户组映射表（用于根据ID快速查找用户组信息）
const userGroupMap = ref<Map<string, UserGroupRecord>>(new Map())
// 用户组选项列表（用于下拉框显示）
const userGroupOptions = ref<UserGroupRecord[]>([])

// 表格列定义
const columns = [
  {
    title: '用户名',
    dataIndex: 'username',
    width: 150,
    ellipsis: true,
    tooltip: true
  },
  {
    title: '姓名',
    dataIndex: 'displayName',
    width: 120,
    ellipsis: true
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    width: 200,
    ellipsis: true,
    tooltip: true
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    width: 130
  },
  {
    title: '认证方式',
    dataIndex: 'authType',
    width: 100,
    slotName: 'authType' // 使用自定义插槽显示
  },
  {
    title: '用户状态',
    dataIndex: 'status',
    width: 100,
    slotName: 'status' // 使用自定义插槽显示状态指示灯
  },
  {
    title: '身份证号',
    dataIndex: 'idCard',
    width: 200,
    ellipsis: true
  },
  {
    title: '用户组',
    dataIndex: 'groupIds',
    width: 200,
    ellipsis: true,
    slotName: 'userGroups'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 200,
    ellipsis: true
  },
  {
    title: '最后登录时间',
    dataIndex: 'lastLoginTime',
    width: 200,
    ellipsis: true
  },
  {
    title: '操作',
    slotName: 'action',
    width: 200,
    fixed: 'right' // 固定在右侧
  }
]

/**
 * 分页配置
 */
const pagination = reactive({
  current: 1, // 当前页码
  pageSize: 10, // 每页条数
  total: 0, // 总条数
  showTotal: true, // 显示总数
  showPageSize: true // 显示每页条数选择器
})

/**
 * 搜索功能相关
 */
// 执行搜索
const handleSearch = () => {
  pagination.current = 1 // 重置到第一页
  loadTableData()
}

// 重置搜索条件
const handleReset = () => {
  // 清空所有搜索条件
  Object.assign(searchForm, {
    displayName: '',
    username: '',
    groupId: '',
    phone: '',
    authType: undefined,
    status: undefined
  })
  pagination.current = 1 // 重置到第一页
  loadTableData()
}

/**
 * 数据加载相关
 */
// 获取用户组列表，构建映射表
const loadUserGroups = async () => {
  try {
    const response = await getUserGroupListApi({ page: 1, pageSize: 1000 })
    if (response.code === 200) {
      const groupMap = new Map<string, UserGroupRecord>()
      response.data.userGroups.forEach(group => {
        if (group.id) {
          groupMap.set(group.id, group)
        }
      })
      userGroupMap.value = groupMap
      userGroupOptions.value = response.data.userGroups
    }
  } catch (error) {
    console.error('获取用户组列表失败:', error)
  }
}

// 根据用户组ID数组获取用户组名称
const getUserGroupNames = (groupIds?: string[]) => {
  if (!groupIds || groupIds.length === 0) {
    return []
  }
  return groupIds
    .map(id => userGroupMap.value.get(id)?.name)
    .filter(Boolean) as string[]
}

// 加载表格数据
const loadTableData = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params: UserListParams = {
      page: pagination.current,
      pageSize: pagination.pageSize
    }

    // 添加搜索条件（只传递非空值）
    if (searchForm.displayName.trim()) {
      params.displayName = searchForm.displayName.trim()
    }
    if (searchForm.username.trim()) {
      params.username = searchForm.username.trim()
    }
    if (searchForm.groupId.trim()) {
      params.groupId = searchForm.groupId.trim()
    }
    if (searchForm.phone.trim()) {
      params.phone = searchForm.phone.trim()
    }
    if (searchForm.authType) {
      params.authType = searchForm.authType
    }
    if (searchForm.status) {
      params.status = searchForm.status
    }

    // 调用API获取数据
    const response = await getUserListApi(params)

    // 设置表格数据和分页信息
    if (response.data && response.data.users) {
      tableData.value = response.data.users
      pagination.total = response.data.total || 0
    } else {
      console.error('API响应数据格式不正确:', response)
      tableData.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('加载用户数据失败:', error)

    // 发生错误时清空数据
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

/**
 * 操作按钮功能
 */
// 新增用户弹窗显示状态
const showAddModal = ref(false)
// 编辑用户弹窗显示状态
const showEditModal = ref(false)
// 当前编辑的用户数据
const currentEditUser = ref<UserRecord>()

// 新建用户
const handleCreate = () => {
  showAddModal.value = true
}

// 编辑用户
const handleEdit = (record: UserRecord) => {
  currentEditUser.value = record
  showEditModal.value = true
}

// 新增成功回调
const handleAddSuccess = () => {
  loadTableData() // 重新加载数据
}

// 编辑成功回调
const handleEditSuccess = () => {
  loadTableData() // 重新加载数据
}

// 刷新数据
const handleRefresh = () => {
  loadTableData()
}

/**
 * 表格操作列功能
 */
// 切换用户状态（启用/禁用）
const handleToggleStatus = async (record: UserRecord) => {
  const action = record.status === 'enable' ? '禁用' : '启用'
  const apiCall = record.status === 'enable' ? disableUserApi : enableUserApi

  try {
    await apiCall(record.id!)
    AMessage.success(`用户${action}成功`)
    // 重新加载数据
    loadTableData()
  } catch (error) {
    AMessage.error(`用户${action}失败，请重试`)
    console.error(`${action}用户失败:`, error)
  }
}

// 删除用户
const handleDelete = async (record: UserRecord) => {
  // 显示确认对话框
  AModal.confirm({
    title: '确认删除',
    content: `确定要删除用户 "${record.displayName}" 吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await deleteUserApi(record.id!)
        AMessage.success('用户删除成功')
        // 重新加载数据
        loadTableData()
      } catch (error) {
        AMessage.error('用户删除失败，请重试')
        console.error('删除用户失败:', error)
      }
    }
  })
}

/**
 * 分页事件处理
 */
// 页码变化
const handlePageChange = (page: number) => {
  pagination.current = page
  loadTableData()
}

// 每页条数变化
const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1 // 重置到第一页
  loadTableData()
}

/**
 * 生命周期
 */
// 组件挂载时加载数据
onMounted(async () => {
  await loadUserGroups() // 先加载用户组数据
  loadTableData() // 然后加载用户数据
})
</script>

<template>
  <a-card class="rounded-[5px]" :bordered="false">
    <!-- 搜索区域 -->
    <a-row>
      <a-col :flex="1">
        <a-form
          ref="searchFormRef"
          :model="searchForm"
          :label-col-props="{ span: 6 }"
          :wrapper-col-props="{ span: 18 }"
          label-align="left"
        >
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="用户名" field="username">
                <a-input
                  v-model="searchForm.username"
                  placeholder="请输入用户名"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="姓名" field="displayName">
                <a-input
                  v-model="searchForm.displayName"
                  placeholder="请输入姓名"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="用户组" field="groupId">
                <a-select
                  v-model="searchForm.groupId"
                  placeholder="请选择用户组"
                  allow-clear
                >
                  <a-option
                    v-for="group in userGroupOptions"
                    :key="group.id"
                    :value="group.id"
                  >
                    {{ group.name }}
                  </a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="手机号" field="phone">
                <a-input
                  v-model="searchForm.phone"
                  placeholder="请输入手机号"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="认证方式" field="authType">
                <a-select
                  v-model="searchForm.authType"
                  placeholder="请选择认证方式"
                  allow-clear
                >
                  <a-option value="local">本地认证</a-option>
                  <a-option value="ldap">LDAP</a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="用户状态" field="status">
                <a-select
                  v-model="searchForm.status"
                  placeholder="请选择用户状态"
                  allow-clear
                >
                  <a-option value="enable">启用</a-option>
                  <a-option value="disable">禁用</a-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-col>

      <a-divider style="height: 84px" direction="vertical" />

      <a-col :flex="'86px'" style="text-align: right">
        <a-space direction="vertical" :size="18">
          <a-button type="primary" @click="handleSearch">
            <template #icon>
              <IconRiSearchLine />
            </template>
            搜索
          </a-button>
          <a-button @click="handleReset">
            <template #icon>
              <IconRiRefreshLine />
            </template>
            重置
          </a-button>
        </a-space>
      </a-col>
    </a-row>

    <a-divider style="margin-top: 0" />

    <!-- 操作按钮区域 -->
    <a-row class="mb-4">
      <a-col :span="12">
        <a-button type="primary" @click="handleCreate">
          <template #icon>
            <IconRiAddLine />
          </template>
          新增
        </a-button>
      </a-col>
      <a-col :span="12" class="flex items-center justify-end">
        <a-tooltip content="刷新">
          <a-button type="text" @click="handleRefresh">
            <icon-tabler-refresh class="text-[var(--color-neutral-10)]" />
          </a-button>
        </a-tooltip>
      </a-col>
    </a-row>

    <!-- 表格区域 -->
    <div>
      <a-table
        row-key="id"
        :columns="columns"
        :data="tableData"
        :loading="{ loading: loading, tip: '加载中...' }"
        :pagination="pagination"
        :bordered="false"
        :scroll="{ x: 1400 }"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      >
        <!-- 空数据状态自定义 -->
        <template #empty>
          <a-empty description="暂无数据" />
        </template>
        <!-- 用户组列 -->
        <template #userGroups="{ record }">
          <div v-if="record.groupIds && record.groupIds.length > 0">
            <a-space size="mini">
              <a-tag
                v-for="groupName in getUserGroupNames(record.groupIds)"
                :key="groupName"
                color="blue"
              >
                {{ groupName }}
              </a-tag>
            </a-space>
          </div>
        </template>
        <template #authType="{ record }">
          {{
            record.authType === 'local'
              ? '本地认证'
              : record.authType === 'ldap'
                ? 'LDAP'
                : '单点登录'
          }}
        </template>
        <template #status="{ record }">
          <div class="flex items-center gap-1">
            <IconRiCircleFill
              :class="
                record.status === 'enable' ? 'text-green-500' : 'text-red-500'
              "
              class="w-2 h-2"
            />
            <span>{{ record.status === 'enable' ? '启用' : '禁用' }}</span>
          </div>
        </template>
        <template #action="{ record }">
          <a-space size="mini">
            <a-button
              type="text"
              size="mini"
              :status="record.status === 'enable' ? 'warning' : 'success'"
              @click="handleToggleStatus(record)"
            >
              {{ record.status === 'enable' ? '禁用' : '启用' }}
            </a-button>
            <a-divider direction="vertical" :margin="0" />
            <a-button type="text" size="mini" @click="handleEdit(record)">
              编辑
            </a-button>
            <a-divider direction="vertical" :margin="0" />
            <a-button
              type="text"
              size="mini"
              status="danger"
              @click="handleDelete(record)"
            >
              删除
            </a-button>
          </a-space>
        </template>
      </a-table>
    </div>

    <!-- 新增用户弹窗 -->
    <AddUser v-model:visible="showAddModal" @success="handleAddSuccess" />

    <!-- 编辑用户弹窗 -->
    <EditUser
      v-model:visible="showEditModal"
      :user-data="currentEditUser"
      @success="handleEditSuccess"
    />
  </a-card>
</template>

<style scoped></style>
