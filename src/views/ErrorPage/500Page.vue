<!--
  500 服务器错误页面
  
  功能说明：
  - 显示服务器连接失败的错误信息
  - 提供重试功能，通过健康检查API判断服务器状态
  - 重试成功后自动跳转到失败前的页面
  - 重试失败后显示警告并启动3秒倒计时
  - 提供返回登录页的备用选项
-->

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'

// 路由实例
const router = useRouter()

// 响应式数据
/** 是否正在重试中 */
const isRetrying = ref(false)
/** 倒计时秒数（重试失败后的冷却时间） */
const countdown = ref(0)
/** 失败前的页面路径，用于重试成功后跳转 */
const previousRoute = ref<string>('')

// 重试按钮显示的文本
const retryButtonText = computed(() => {
  if (isRetrying.value) {
    return '重试中...'
  }
  if (countdown.value > 0) {
    return `重试 (${countdown.value}s)`
  }
  return '重试'
})

// 倒计时定时器引用
let countdownTimer: number | null = null

// 组件挂载时获取失败前的页面路径
onMounted(() => {
  // 尝试从sessionStorage获取失败前的路径
  const lastRoute = sessionStorage.getItem('lastRoute')
  if (lastRoute && lastRoute !== '/500') {
    previousRoute.value = lastRoute
  } else {
    // 如果没有记录，默认跳转到登录页
    previousRoute.value = '/login'
  }
})

// 组件卸载时清理定时器，防止内存泄漏
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
})
// 服务器健康检查，直接使用axios避免触发封装的request拦截器
const checkHealth = async (): Promise<boolean> => {
  try {
    await axios.get(`${import.meta.env.VITE_API_BASE_URL}/health`, {
      timeout: 5000
    })
    return true
  } catch {
    return false
  }
}

// 开始3秒倒计时，重试失败后禁用重试按钮
const startCountdown = () => {
  countdown.value = 3
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      if (countdownTimer) {
        clearInterval(countdownTimer)
        countdownTimer = null
      }
    }
  }, 1000)
}

// 处理重试逻辑
const handleRetry = async () => {
  // 防止重复点击
  if (countdown.value > 0) {
    return
  }

  try {
    isRetrying.value = true
    const isHealthy = await checkHealth()

    if (isHealthy) {
      AMessage.success({
        content: '服务器恢复正常，正在跳转...',
        duration: 2000
      })
      // 健康检查成功，跳转到失败前的页面
      router.push(previousRoute.value)
    } else {
      // 健康检查失败，显示警告并开始倒计时
      AMessage.warning({
        content: '服务器请求失败，请稍后再试',
        duration: 3000
      })
      startCountdown()
    }
  } catch {
    // 请求异常，显示警告并开始倒计时
    AMessage.warning({
      content: '服务器请求失败，请稍后再试',
      duration: 3000
    })
    startCountdown()
  } finally {
    isRetrying.value = false
  }
}

// 返回登录页
const goHome = () => {
  router.push('/login')
}
</script>

<template>
  <!-- 500错误页面主容器 -->
  <div
    class="flex items-center justify-center min-h-screen bg-[var(--color-bg-1)] p-5 text-center"
  >
    <div>
      <!-- 错误图标 -->
      <div
        class="w-100px h-100px rounded-[50%] bg-[var(--color-fill-1)] flex justify-center items-center mx-auto mb-6"
      >
        <icon-tabler-server-off
          class="text-60px text-[var(--color-neutral-10)]"
        />
      </div>

      <!-- 错误标题 -->
      <div class="text-20px font-semibold text-[var(--color-neutral-10)] mb-4">
        服务器内部错误
      </div>

      <!-- 错误描述 -->
      <div class="text-[var(--color-neutral-8)] mb-8">
        服务器出现异常，请稍后重试或联系管理员
      </div>

      <!-- 操作按钮组 -->
      <div class="flex gap-3 justify-center">
        <!-- 重试按钮 -->
        <a-button
          size="large"
          shape="round"
          type="primary"
          :loading="isRetrying"
          :disabled="countdown > 0"
          @click="handleRetry"
        >
          <template #icon>
            <icon-material-symbols-refresh />
          </template>
          {{ retryButtonText }}
        </a-button>

        <!-- 返回登录页按钮 -->
        <a-button size="large" shape="round" @click="goHome">
          <template #icon>
            <icon-material-symbols-home-outline />
          </template>
          返回登录页
        </a-button>
      </div>
    </div>
  </div>
</template>
