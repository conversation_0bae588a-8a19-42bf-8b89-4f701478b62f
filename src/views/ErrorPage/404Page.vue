<!--
  404 页面未找到错误页面
  
  功能说明：
  - 显示页面未找到的错误信息
  - 提供友好的用户提示和操作指引
  - 支持返回上一页和返回首页功能
  - 提供清晰的视觉反馈和操作按钮
-->

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 路由实例
const router = useRouter()
// 用户状态管理
const userStore = useUserStore()

/**
 * 返回上一页
 * 使用浏览器历史返回功能
 */
const goBack = () => {
  // 检查是否有历史记录
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    // 如果没有历史记录，跳转到首页
    goHome()
  }
}

/**
 * 返回首页
 * 根据用户登录状态跳转到合适的页面
 */
const goHome = () => {
  if (userStore.isLoggedIn) {
    // 已登录用户跳转到仪表板
    router.push('/dashboard')
  } else {
    // 未登录用户跳转到登录页
    router.push('/login')
  }
}
</script>

<template>
  <!-- 404错误页面主容器 -->
  <div
    class="flex items-center justify-center min-h-screen bg-[var(--color-bg-1)] p-5 text-center"
  >
    <div>
      <!-- 错误图标 -->
      <div
        class="w-100px h-100px rounded-[50%] bg-[var(--color-fill-1)] flex justify-center items-center mx-auto mb-6"
      >
        <icon-tabler-error-404
          class="text-60px text-[var(--color-neutral-10)]"
        />
      </div>
      
      <!-- 错误标题 -->
      <div class="text-20px font-semibold text-[var(--color-neutral-10)] mb-4">
        页面未找到
      </div>
      
      <!-- 错误描述 -->
      <div class="text-[var(--color-neutral-8)] mb-8">
        抱歉，您访问的页面不存在或已被移除
      </div>
      
      <!-- 操作按钮组 -->
      <div class="flex gap-3 justify-center">
        <!-- 返回上一页按钮 -->
        <a-button size="large" shape="round" @click="goBack">
          <template #icon>
            <icon-material-symbols-arrow-back />
          </template>
          返回上一页
        </a-button>
        
        <!-- 返回首页按钮 -->
        <a-button type="primary" size="large" shape="round" @click="goHome">
          <template #icon>
            <icon-material-symbols-home-outline />
          </template>
          返回首页
        </a-button>
      </div>
    </div>
  </div>
</template>
