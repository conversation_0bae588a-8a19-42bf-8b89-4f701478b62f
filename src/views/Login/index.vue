<script setup lang="ts">
import { useUserStore } from '@/stores/user'
import type { LoginForm } from '@/api/auth'

const router = useRouter()
const userStore = useUserStore()

const loginFormRef = ref()

const loginForm = reactive<LoginForm>({
  username: '',
  password: '',
  authType: 'local'
})

// 验证码输入
const captcha = ref('')

// 生成的验证码
const identifyCode = ref('')

// 校验规则
const loginFormRules = ref({
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  captcha: [
    {
      validator: (_value: any, callback: (error?: string) => void) => {
        const captchaValue = captcha.value
        if (!captchaValue) {
          callback('请输入验证码')
          return
        }
        if (captchaValue.toLowerCase() !== identifyCode.value.toLowerCase()) {
          callback('验证码错误')
          return
        }
        callback()
      },
      trigger: 'blur'
    }
  ]
})

// 登录按钮加载
const loginButtonLoading = ref(false)

// 生成一个随机数
const randomNum = (min: number, max: number) => {
  return Math.floor(Math.random() * (max - min) + min)
}

// 绘制验证码，默认长度为 4
const drawIdentifyCode = () => {
  const chars = '1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
  identifyCode.value = ''

  for (let i = 0; i < 4; i++) {
    identifyCode.value += chars.charAt(randomNum(0, chars.length))
  }
}

const handleLogin = async () => {
  try {
    loginButtonLoading.value = true

    // 验证表单是否有错误
    const validateResult = await loginFormRef.value?.validate()
    if (validateResult) {
      return
    }

    // 表单验证成功，执行登录逻辑
    await userStore.login(loginForm)

    AMessage.success({
      closable: true,
      content: '登录成功'
    })

    // 登录成功后跳转到主页
    router.push('/')
  } catch {
    // 登录失败时刷新验证码
    drawIdentifyCode()
  } finally {
    loginButtonLoading.value = false
  }
}

onMounted(() => {
  drawIdentifyCode()
})
</script>

<template>
  <div class="flex w-full h-screen bg-[var(--color-bg-1)]">
    <!-- 左侧内容区域 -->
    <div class="w-1/2 relative bg-gray-50 flex flex-col rounded-r-3xl">
      <!-- Logo 在左上角 -->
      <div class="absolute top-5 left-5 z-10">
        <icon-svg-logo class="h-13 w-auto" />
      </div>

      <!-- 居中的 SVG 图标 -->
      <div class="flex-1 flex items-center justify-center">
        <icon-svg-login-left class="w-65 h-65" />
      </div>
    </div>

    <!-- 右侧登录表单 -->
    <div class="w-1/2 flex items-center justify-center px-8">
      <div class="w-full max-w-md">
        <div class="mb-8 text-center">
          <a-typography>
            <a-typography-title :heading="3">
              登录—盾山运维平台
            </a-typography-title>
            <a-typography-text type="secondary">
              欢迎回来，请输入您的登录信息进行登录。
            </a-typography-text>
          </a-typography>
        </div>

        <a-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginFormRules"
          layout="vertical"
          @submit="handleLogin"
        >
          <a-form-item field="username" label="用户名" hide-asterisk>
            <a-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              allow-clear
              size="large"
            >
              <template #prefix>
                <IconPhUserBold />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item field="password" label="密码" hide-asterisk>
            <a-input-password
              v-model="loginForm.password"
              placeholder="请输入密码"
              allow-clear
              size="large"
            >
              <template #prefix>
                <IconPhLockBold />
              </template>
            </a-input-password>
          </a-form-item>

          <a-form-item field="captcha" label="验证码" hide-asterisk>
            <a-input
              v-model="captcha"
              placeholder="请输入验证码"
              allow-clear
              size="large"
            >
              <template #prefix>
                <IconCiWavyCheck />
              </template>
            </a-input>
            <div class="ml-2">
              <IdentifyCode
                :identify-code="identifyCode"
                @click="drawIdentifyCode()"
              />
            </div>
          </a-form-item>

          <a-form-item>
            <a-button
              type="primary"
              html-type="submit"
              size="large"
              :loading="loginButtonLoading"
              long
            >
              登录
            </a-button>
          </a-form-item>
        </a-form>

        <div class="text-right">
          <a-link :hoverable="false">忘记密码？</a-link>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 表单 label 加粗 */
:deep(.arco-form-item-label-col) {
  font-weight: 500;
}
</style>
