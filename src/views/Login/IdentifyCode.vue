<template>
  <div class="flex items-center h-full">
    <canvas
      id="id-canvas"
      class="id-canvas"
      :width="props.contentWidth"
      :height="props.contentHeight"
    ></canvas>
  </div>
</template>

<script setup lang="ts" name="Identify">
interface IProps {
  identifyCode?: string // 默认注册码
  contentWidth?: number // 容器宽度
  contentHeight?: number // 容器高度
}

const props = withDefaults(defineProps<IProps>(), {
  identifyCode: '12ab',
  contentWidth: 100,
  contentHeight: 36
})

const fontSizeMin = 25
const fontSizeMax = 35

// 生成一个随机数
const randomNum = (min: number, max: number) => {
  return Math.floor(Math.random() * (max - min) + min)
}

// 生成一个随机的颜色
const randomColor = (min: number, max: number) => {
  const r = randomNum(min, max)
  const g = randomNum(min, max)
  const b = randomNum(min, max)
  return 'rgb(' + r + ',' + g + ',' + b + ')'
}

const drawPic = () => {
  const canvas = document.getElementById('id-canvas') as HTMLCanvasElement
  const ctx = canvas.getContext('2d') as CanvasRenderingContext2D
  ctx.textBaseline = 'bottom'

  // 绘制背景
  ctx.fillStyle = '#e6ecfd'
  ctx.fillRect(0, 0, props.contentWidth, props.contentHeight)

  // 绘制文字
  for (let i = 0; i < props.identifyCode.length; i++) {
    drawText(ctx, props.identifyCode[i], i)
  }

  drawLine(ctx)
  drawDot(ctx)
}

const drawText = (ctx: CanvasRenderingContext2D, txt: string, i: number) => {
  ctx.fillStyle = randomColor(50, 160) // 随机生成字体颜色
  ctx.font = randomNum(fontSizeMin, fontSizeMax) + 'px SimHei' // 随机生成字体大小
  const x = (i + 1) * (props.contentWidth / (props.identifyCode.length + 1))
  const y = randomNum(fontSizeMax, props.contentHeight - 5)
  const deg = randomNum(-30, 30)
  // 修改坐标原点和旋转角度
  ctx.translate(x, y)
  ctx.rotate((deg * Math.PI) / 180)
  ctx.fillText(txt, 0, 0)
  // 恢复坐标原点和旋转角度
  ctx.rotate((-deg * Math.PI) / 180)
  ctx.translate(-x, -y)
}

const drawLine = (ctx: CanvasRenderingContext2D) => {
  // 绘制干扰线
  for (let i = 0; i < 3; i++) {
    ctx.strokeStyle = randomColor(100, 200)
    ctx.beginPath()
    ctx.moveTo(
      randomNum(0, props.contentWidth),
      randomNum(0, props.contentHeight)
    )
    ctx.lineTo(
      randomNum(0, props.contentWidth),
      randomNum(0, props.contentHeight)
    )
    ctx.stroke()
  }
}

const drawDot = (ctx: CanvasRenderingContext2D) => {
  // 绘制干扰点，减少数量
  for (let i = 0; i < 10; i++) {
    ctx.fillStyle = randomColor(180, 220)
    ctx.beginPath()
    ctx.arc(
      randomNum(0, props.contentWidth),
      randomNum(0, props.contentHeight),
      1,
      0,
      2 * Math.PI
    )
    ctx.fill()
  }
}

onMounted(() => {
  drawPic()
})

watch(() => props.identifyCode, drawPic)
</script>

<style scoped></style>
