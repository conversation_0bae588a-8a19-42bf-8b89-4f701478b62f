// constantRoutes.ts 定义所有的常量路由信息,也就是所有不需要权限的路由
import type { RouteRecordRaw } from 'vue-router'
import { markRaw } from 'vue'
import dashboardIcon from '~icons/material-symbols-light/dashboard'
import userCenterIcon from '~icons/material-symbols-light/person'
import userManagementIcon from '~icons/material-symbols-light/group'
import userGroupManagementIcon from '~icons/material-symbols-light/groups'
import assetManagementIcon from '~icons/material-symbols-light/inventory-2'
import hostManagementIcon from '~icons/material-symbols-light/computer'
import { menuRouterFormat } from '@/utils/menuRouter'

// 静态路由
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login/index.vue'),
    meta: {
      title: '登录',
      hidden: true
    }
  },
  {
    // 默认首页
    path: '/',
    name: 'layout',
    component: () => import('@/layout/DefaultLayout.vue'),
    meta: {
      title: '首页',
      hidden: false
    },
    redirect: { name: 'Dashboard' },
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard/index.vue'),
        meta: {
          title: '工作台',
          icon: markRaw(dashboardIcon),
          hidden: false
        }
      }
    ]
  },
  {
    path: '/user-center',
    name: 'UserCenter',
    component: () => import('@/layout/DefaultLayout.vue'),
    meta: {
      title: '用户中心',
      icon: markRaw(userCenterIcon),
      hidden: false
    },
    redirect: { name: 'UserManagement' },
    children: [
      {
        path: '/user-center/user-management',
        name: 'UserManagement',
        component: () => import('@/views/UserCenter/UserManagement/index.vue'),
        meta: {
          title: '用户管理',
          icon: markRaw(userManagementIcon),
          hidden: false
        }
      },
      {
        path: '/user-center/user-group-management',
        name: 'UserGroupManagement',
        component: () =>
          import('@/views/UserCenter/UserGroupManagement/index.vue'),
        meta: {
          title: '用户组管理',
          icon: markRaw(userGroupManagementIcon),
          hidden: false
        }
      }
    ]
  },
  {
    path: '/asset-management',
    name: 'AssetManagement',
    component: () => import('@/layout/DefaultLayout.vue'),
    meta: {
      title: '资产管理',
      icon: markRaw(assetManagementIcon),
      hidden: false
    },
    redirect: { name: 'HostManagement' },
    children: [
      {
        path: '/asset-management/host-management',
        name: 'HostManagement',
        component: () =>
          import('@/views/AssetManagement/HostManagement/index.vue'),
        meta: {
          title: '主机资产',
          icon: markRaw(hostManagementIcon),
          hidden: false
        }
      },
      {
        path: 'host-management/detail/:id',
        name: 'HostDetail',
        component: () =>
          import('@/views/AssetManagement/HostManagement/detail/index.vue'),
        meta: {
          title: '主机详情',
          hidden: true
        }
      }
    ]
  },
  {
    path: '/500',
    name: 'ServerError',
    component: () => import('@/views/ErrorPage/500Page.vue'),
    meta: {
      title: '500',
      hidden: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/ErrorPage/404Page.vue'),
    meta: {
      title: '404',
      hidden: true
    }
  }
]

// 解析后 路由菜单列表
// 使用 [...menuRouter] - 因为这个函数会直接修改路由对象的
// path 属性，使用扩展运算符创建副本可以避免修改原始数据。
export const menuRouterFormatList = menuRouterFormat([...constantRoutes])
