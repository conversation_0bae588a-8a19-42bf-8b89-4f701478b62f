/**
 * 认证方式类型
 * local: 本地认证 - 使用系统内部账号密码验证
 * ldap: LDAP认证 - 使用轻型目录访问协议验证
 */
export type UserAuthType = 'local' | 'ldap'

/**
 * 用户状态类型
 * enable: 启用状态 - 用户可以正常登录和使用系统
 * disable: 禁用状态 - 用户被禁止登录系统
 */
export type UserStatus = 'enable' | 'disable'

/**
 * 用户记录接口 - 统一的用户数据结构
 * 用于表格展示、API返回、表单数据等所有场景
 * 后端在返回用户列表时应该忽略或返回空字符串给密码字段
 */
export interface UserRecord {
  /** 用户唯一标识ID（新增时不需要，后端自动生成） */
  id?: string
  /** 用户显示名称（用于展示的真实姓名） */
  displayName: string
  /** 用户登录名称（用于登录的账号名称） */
  username: string
  /** 用户邮箱地址 */
  email?: string
  /** 用户手机号码 */
  phone?: string
  /** 用户认证方式 */
  authType: UserAuthType
  /** 用户当前状态 */
  status: UserStatus
  /** 用户身份证号码 */
  idCard?: string
  /** 用户密码（新增时必填，编辑时可选，查询时后端应返回空字符串或忽略） */
  password?: string
  /** 确认密码（仅用于前端表单验证，不传给后端） */
  confirmPassword?: string
  /** 关联的用户组ID数组（可选） */
  groupIds?: string[]
  /** 用户账号创建时间（后端返回，前端不需要传递） */
  createTime?: string
  /** 用户最后一次登录时间（后端返回，前端不需要传递） */
  lastLoginTime?: string
}
