/**
 * 用户组管理相关类型定义
 * 统一管理用户组功能涉及的所有TypeScript类型接口
 */

/**
 * 用户组基础数据结构
 * 用于表格展示、API返回、表单数据等所有场景的统一数据模型
 */
export interface UserGroupRecord {
  /** 用户组唯一标识ID（新增时不需要，后端自动生成） */
  id?: string
  /** 用户组名称（必填，用于显示和搜索） */
  name: string
  /** 用户组编码（必填，系统内唯一标识） */
  code: string
  /** 用户组描述信息（可选） */
  description?: string
  /** 关联的用户ID数组（用于用户组成员管理） */
  userIds?: string[]
  /** 创建时间（后端返回，前端只读） */
  createTime?: string
  /** 最后更新时间（后端返回，前端只读） */
  updateTime?: string
}

// ==================== API请求参数类型 ====================

/**
 * 创建用户组的请求参数
 * 用于新增用户组时向后端传递的数据结构
 */
export interface CreateUserGroupRequest {
  /** 用户组名称 */
  name: string
  /** 用户组编码 */
  code: string
  /** 用户组描述 */
  description?: string
  /** 关联的用户ID数组 */
  userIds?: string[]
}

/**
 * 更新用户组的请求参数
 * 用于编辑用户组时向后端传递的数据结构，所有字段都是可选的
 */
export interface UpdateUserGroupRequest {
  /** 用户组名称 */
  name?: string
  /** 用户组编码 */
  code?: string
  /** 关联的用户ID数组 */
  userIds?: string[]
  /** 用户组描述 */
  description?: string
}

/**
 * 用户组列表API查询参数
 * 用于调用后端获取用户组列表的参数
 */
export interface UserGroupListParams {
  /** 当前页码，从1开始 */
  page?: number
  /** 每页显示条数 */
  pageSize?: number
  /** 按名称模糊搜索 */
  name?: string
  /** 按编码模糊搜索 */
  code?: string
}

/**
 * 用户组列表API响应数据
 * 后端返回的用户组列表数据结构
 */
export interface RespUserGroupListData {
  /** 用户组列表数据 */
  userGroups: UserGroupRecord[]
  /** 数据总条数（用于分页） */
  total: number
}
