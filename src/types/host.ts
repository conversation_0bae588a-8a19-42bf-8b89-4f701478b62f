/**
 * 硬盘类型
 * ssd: 固态硬盘
 * hdd: 机械硬盘
 */
export type DiskType = 'ssd' | 'hdd'

/**
 * 硬盘信息接口
 */
export interface DiskInfo {
  /** 硬盘标识名称（如：sda、sdb、C盘、D盘等） */
  name: string
  /** 硬盘容量（单位：GB） */
  size: number
  /** 硬盘类型 */
  type: DiskType
}

/**
 * 主机资产记录接口 - 统一的主机数据结构
 * 用于表格展示、API返回、表单数据等所有场景
 */
export interface HostRecord {
  /** 主机唯一标识ID（新增时不需要，后端自动生成） */
  id?: string
  /** 主机名称 */
  hostname: string
  /** 主机IP地址 */
  ipAddress: string
  /** CPU数量（核心数） */
  cpu: number
  /** 内存大小（单位：GB） */
  memory: number
  /** 硬盘列表 */
  disks: DiskInfo[]
  /** 操作系统详细信息（如：Ubuntu 20.04 LTS、Windows Server 2019） */
  operatingSystem: string
  /** 备注信息 */
  remarks?: string
  /** 主机创建时间（后端返回，前端不需要传递） */
  createTime?: string
  /** 主机信息最后更新时间（后端返回，前端不需要传递） */
  updateTime?: string
}
