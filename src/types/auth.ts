/**
 * 认证相关的类型定义
 */

/**
 * 登录表单数据类型
 */
export interface LoginFormData {
  username: string
  password: string
  authType: 'local' | 'ldap' | 'oauth'
}

/**
 * 登录API响应数据类型
 */
export interface LoginResponseData {
  token: string
  refreshToken?: string
  expiresIn?: number
}

/**
 * 用户基本信息类型
 */
export interface UserProfile {
  userId: string
  username: string
  email?: string
  avatar?: string
  nickname?: string
  phone?: string
  roles: string[]
  permissions: string[]
  lastLoginTime?: string
  status: 'active' | 'inactive' | 'locked'
}

/**
 * JWT Token 载荷类型
 */
export interface JWTPayload {
  userId: string
  username: string
  iss: string // 签发者
  exp: number // 过期时间
  nbf: number // 生效时间
  iat: number // 签发时间
}

/**
 * 认证状态类型
 */
export interface AuthState {
  token: string
  refreshToken?: string
  userInfo: UserProfile | null
  isLoggedIn: boolean
  loginTime?: number
}

/**
 * 权限检查结果类型
 */
export interface PermissionCheckResult {
  hasPermission: boolean
  missingPermissions?: string[]
}
