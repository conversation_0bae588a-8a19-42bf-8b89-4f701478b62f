<script lang="ts" setup>
// 侧边栏收缩状态
const collapsed = ref(false)

// 侧边栏收缩触发事件
const handleCollapse = (val: boolean) => {
  collapsed.value = val
}
</script>

<!-- 默认布局组件 - 侧边栏布局 -->
<template>
  <div class="sidebar-layout">
    <!-- 主体布局容器 -->
    <a-layout>
      <!-- 固定顶部导航栏 -->
      <a-affix>
        <a-layout-header>
          <Navbar>
            <!-- 左侧：Logo -->
            <template #left> <Logo /> </template>
            <!-- 中间：主题切换 -->
            <template #center>
              <SwitchMode />
              <SwitchScreen />
            </template>
            <!-- 右侧：用户信息 -->
            <template #right> <Info /> </template>
          </Navbar>
        </a-layout-header>
      </a-affix>

      <!-- 主内容区域布局 -->
      <a-layout>
        <!-- 固定侧边栏 - 距离顶部58px -->
        <a-affix :offsetTop="58">
          <a-layout-sider
            breakpoint="lg"
            :width="220"
            height="calc(100vh-58px)"
            collapsible
            :collapsed="collapsed"
            @collapse="handleCollapse"
          >
            <!-- 导航菜单 -->
            <Menu />
          </a-layout-sider>
        </a-affix>

        <!-- 右侧内容区域 -->
        <a-layout>
          <!-- 主内容区域 -->
          <a-layout-content class="min-h-[calc(100vh-58px)]">
            <!-- 路由视图容器 -->
            <router-view v-slot="{ Component }">
              <component :is="Component" />
            </router-view>
          </a-layout-content>
          <!-- 底部页脚 -->
          <a-layout-footer> <Footer /> </a-layout-footer>
        </a-layout>
      </a-layout>
    </a-layout>
  </div>
</template>

<style scoped>
.sidebar-layout :deep(.arco-layout-header),
.sidebar-layout :deep(.arco-layout-footer),
.sidebar-layout :deep(.arco-layout-content) {
  @apply text-[var(--color-text-1)] text-14px;
}

.sidebar-layout :deep(.arco-layout-header) {
  @apply w-full h-58px;
  @apply bg-[var(--color-bg-3)]  border-b-[var(--color-border-1)] border-b-solid border-b-width-1px box-border;
}
.sidebar-layout :deep(.arco-layout-content) {
  @apply flex flex-col p-15px;
  @apply bg-[var(--color-neutral-2)] relative;
}
.sidebar-layout :deep(.arco-layout-footer) {
  @apply w-full flex justify-center items-center;
  @apply border-t-[var(--color-border-1)] border-t-solid border-t-width-1px box-border;
  @apply bg-[var(--color-bg-2)] text-[var(--color-text-1)] text-14px;
}

.sidebar-layout :deep(.arco-layout-sider) {
  @apply h-[calc(100vh-58px)];
}
.sidebar-layout :deep(.arco-layout-sider),
.sidebar-layout :deep(.arco-layout-sider-trigger) {
  @apply border-r-[var(--color-border-1)] border-r-solid border-r-width-1px box-border;
}
</style>
