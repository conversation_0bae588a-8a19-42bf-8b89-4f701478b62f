<script setup lang="ts">
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 计算属性：获取用户名
const username = computed(() => {
  return (
    userStore.userInfo?.displayName || userStore.userInfo?.username || 'Guest'
  )
})

// 计算属性：获取用户名的最后一个字符作为头像文本
const avatarText = computed(() => {
  return username.value.slice(-1)
})

// 计算属性：获取用户头像（UserRecord中没有avatar字段，暂时返回null）
const userAvatar = computed(() => {
  return null // UserRecord 中没有 avatar 字段
})

// 处理菜单点击事件
const handleMenuClick = (key: string) => {
  switch (key) {
    case 'profile':
      // 跳转到个人设置页面
      console.log('跳转到个人设置')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 处理退出登录
const handleLogout = () => {
  userStore.logout()
}
</script>

<!-- 用户信息下拉菜单组件 -->
<template>
  <div class="user-info">
    <a-dropdown trigger="click" position="bottom" @select="handleMenuClick">
      <a-button shape="round" class="user-button">
        <a-avatar v-if="userAvatar" :size="24" :image-url="userAvatar" />
        <a-avatar v-else :size="24" :style="{ backgroundColor: '#ff7875' }">
          {{ avatarText }}
        </a-avatar>
        <!-- 用户名，支持省略号显示，悬浮显示完整名称 -->
        <span class="username" :title="username">{{ username }}</span>
        <icon-ri-arrow-down-s-line class="text-[var(--color-text-1)]" />
      </a-button>

      <!-- 下拉菜单内容 -->
      <template #content>
        <a-doption value="profile">
          <template #icon>
            <icon-ri-user-settings-line />
          </template>
          <template #default>个人设置</template>
        </a-doption>
        <a-doption value="logout">
          <template #icon>
            <icon-ri-logout-box-line />
          </template>
          <template #default>退出登录</template>
        </a-doption>
      </template>
    </a-dropdown>
  </div>
</template>

<style scoped>
/* 用户信息容器样式 */
.user-info {
  @apply flex items-center;
}

/* 用户信息按钮样式 */
/* 覆盖 a-button 默认样式 */
.user-button.arco-btn {
  @apply gap-1.5 p-1  max-w-50;
}

/* 用户名样式 */
.username {
  @apply text-xs font-medium text-gray-800 whitespace-nowrap overflow-hidden text-ellipsis flex-1 min-w-0 max-w-30;
  @apply text-[var(--color-text-1)];
}

/* 响应式设计 - 在小屏幕上隐藏用户名 */
@media (max-width: 768px) {
  .username {
    @apply hidden;
  }
}
</style>
