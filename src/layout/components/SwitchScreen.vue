<script setup lang="ts">
import fullscreen from '~icons/lucide/fullscreen'
import fullscreenExit from '~icons/mingcute/fullscreen-exit-fill'
import { useFullscreen } from '@vueuse/core'

// 使用 VueUse 的全屏功能
const { toggle: toggleFullScreen, isFullscreen } = useFullscreen()

// 当前显示的图标（基于全屏状态动态计算）
const currentIcon = computed(() => {
  return isFullscreen.value ? markRaw(fullscreenExit) : markRaw(fullscreen)
})

// 当前提示文本（基于全屏状态动态计算）
const tooltipContent = computed(() => {
  return isFullscreen.value ? '点击退出全屏模式' : '点击切换全屏模式'
})

// 处理按钮点击事件（直接切换全屏状态）
const handleToggleScreen = () => {
  toggleFullScreen()
}
</script>

<template>
  <!-- 全屏切换按钮，带提示信息 -->
  <a-tooltip :content="tooltipContent">
    <a-button shape="circle" mini @click="handleToggleScreen">
      <template #icon>
        <component
          :is="currentIcon"
          class="text-[var(--color-text-1)] text-16px"
        />
      </template>
    </a-button>
  </a-tooltip>
</template>
