<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useSystemStore } from '@/stores/system.ts'

// 获取系统设置 store 实例
const systemStore = useSystemStore()

// 初始化主题模式（处理从本地存储恢复的数据）
systemStore.initMode()

// 响应式引用当前模式和模式列表
const { currentMode, modeList } = storeToRefs(systemStore)

// 处理下拉菜单选中事件
const handleSelect = (selectedMode: any) => {
  currentMode.value = selectedMode
}
</script>

<template>
  <!-- 主题切换下拉菜单 -->
  <a-dropdown @select="handleSelect" trigger="hover" class="mode-dropdown">
    <!-- 主题切换按钮，显示当前主题对应的图标 -->
    <a-button shape="circle">
      <template #icon>
        <component
          :is="currentMode?.icon"
          class="text-[var(--color-text-1)] text-16px"
        />
      </template>
    </a-button>

    <!-- 下拉菜单内容 -->
    <template #content>
      <a-doption v-for="item of modeList" :key="item.name" :value="item">
        <!-- 当前选中项显示对勾图标 -->
        <template #icon v-if="currentMode?.name === item.name">
          <icon-material-symbols-check-small
            class="text-[var(--color-text-1)] text-14px"
          />
        </template>
        <template #default>{{ item.title }}</template>
      </a-doption>
    </template>
  </a-dropdown>
</template>

<style scoped>
/* 调整下拉菜单选项的布局，图标在右侧 */
.mode-dropdown .arco-dropdown-option {
  @apply flex justify-end items-center;
}
</style>
