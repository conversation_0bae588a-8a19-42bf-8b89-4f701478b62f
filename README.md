# 盾山运维平台 - 前端管理系统

盾山运维平台（DunshanOps）的前端管理系统，基于 Vue 3 + TypeScript + Vite 构建的现代化运维管理界面。

## 项目简介

盾山运维平台前端是一个企业级运维管理系统的用户界面，提供直观、高效的运维操作体验。项目采用最新的前端技术栈，确保良好的开发体验和用户体验。

## 技术栈

- **框架**: Vue 3.5.17 (Composition API)
- **语言**: TypeScript 5.8
- **构建工具**: Vite 7.0
- **路由**: Vue Router 4.5
- **状态管理**: Pinia 3.0
- **UI 组件库**: Arco Design Vue 2.57.0
- **CSS 框架**: UnoCSS 66.3.3
- **代码规范**: ESLint + Prettier
- **开发工具**: Vue DevTools

## 插件配置

### 自动化插件

- **unplugin-auto-import**: 自动导入 Vue、Pinia、Vue Router 等 API

- **unplugin-vue-components**: 自动导入组件

### 样式插件

- **UnoCSS**: 原子化 CSS 框架
  - **@unocss/preset-wind**: Tailwind CSS 兼容预设
  - **@unocss/preset-attributify**: 属性化模式支持
  - **@unocss/transformer-directives**: CSS 指令转换器
  - 支持 `@apply`、`@screen` 等指令

### UI 组件库

- **@arco-design/web-vue**: 企业级 Vue 3 UI 组件库

### 图标系统

- **unplugin-icons**: 图标自动导入插件
- **@iconify/vue**: Iconify 图标库 Vue 集成

## 项目结构

```
DunshanOps-admin/
├── .vscode/                # VSCode 配置
├── public/                 # 静态资源
│   └── favicon.ico         # 网站图标
├── src/                    # 源代码目录
│   ├── api/                # API 接口层
│   │   └── auth.ts         # 认证相关接口
│   ├── assets/             # 静态资源 (图片、样式等)
│   ├── components/         # 公共组件
│   ├── router/             # 路由配置
│   │   └── index.ts        # 路由定义
│   ├── stores/             # Pinia 状态管理
│   ├── styles/             # 全局样式文件
│   │   └── normalize.css   # 样式重置文件
│   ├── types/              # 类型定义
│   │   └── global.ts       # 全局类型定义
│   ├── utils/              # 工具函数
│   │   └── request.ts      # HTTP 请求封装
│   ├── views/              # 页面组件
│   │   ├── Home/           # 首页模块
│   │   │   └── index.vue   # 首页组件
│   │   └── Login/          # 登录模块
│   │       ├── index.vue   # 登录页面
│   │       └── IdentifyCode.vue # 验证码组件
│   ├── App.vue             # 根组件
│   └── main.ts             # 应用入口文件
├── .editorconfig           # 编辑器配置
├── .eslintrc-auto-import.json # 自动导入 ESLint 配置
├── .gitignore              # Git 忽略文件配置
├── auto-imports.d.ts       # 自动导入类型声明
├── components.d.ts         # 组件类型声明
├── env.d.ts                # 环境变量类型定义
├── eslint.config.ts        # ESLint 配置
├── index.html              # HTML 模板
├── package.json            # 项目依赖配置
├── package-lock.json       # 依赖锁定文件
├── tsconfig.json           # TypeScript 主配置
├── tsconfig.app.json       # 应用 TypeScript 配置
├── tsconfig.node.json      # Node.js TypeScript 配置
├── vite.config.ts          # Vite 构建配置
└── README.md               # 项目说明文档
```

## 开发环境要求

- Node.js >= 18.0.0
- npm >= 8.0.0

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

开发服务器将在 `http://localhost:5173` 启动，支持热重载。

### 3. 构建生产版本

```bash
npm run build
```

构建产物将输出到 `dist` 目录。

## 可用脚本

| 命令                 | 说明                |
| -------------------- | ------------------- |
| `npm run dev`        | 启动开发服务器      |
| `npm run build`      | 构建生产版本        |
| `npm run preview`    | 预览生产构建        |
| `npm run type-check` | TypeScript 类型检查 |
| `npm run lint`       | 代码检查和自动修复  |
| `npm run format`     | 代码格式化          |

## 代码规范

项目集成了 ESLint 和 Prettier 来确保代码质量和一致性：

- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Vue 官方规范**: 遵循 Vue 3 最佳实践

## 配置说明

### Vite 配置

- 配置了路径别名 `@` 指向 `src` 目录
- 集成 Vue DevTools 用于开发调试
- 支持热模块替换 (HMR)

## 许可证

本项目采用私有许可证，仅供内部使用。
