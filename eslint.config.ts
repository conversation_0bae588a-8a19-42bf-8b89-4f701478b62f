import { globalIgnores } from 'eslint/config'
import {
  defineConfigWithVueTs,
  vueTsConfigs
} from '@vue/eslint-config-typescript'
import pluginVue from 'eslint-plugin-vue'
import skipFormatting from '@vue/eslint-config-prettier/skip-formatting'

import autoImportGlobals from './.eslintrc-auto-import.json' assert { type: 'json' }

// To allow more languages other than `ts` in `.vue` files, uncomment the following lines:
// import { configureVueProject } from '@vue/eslint-config-typescript'
// configureVueProject({ scriptLangs: ['ts', 'tsx'] })
// More info at https://github.com/vuejs/eslint-config-typescript/#advanced-setup

export default defineConfigWithVueTs(
  {
    name: 'app/files-to-lint',
    files: ['**/*.{ts,mts,tsx,vue}']
  },

  {
    languageOptions: {
      // 配置全局变量，使 ESLint 识别自动导入的变量
      // 将 .eslintrc-auto-import.json 中定义的全局变量添加到 ESLint 配置中
      // 这样使用 unplugin-auto-import 自动导入的 API（如 ref, computed, AMessage 等）
      // 不会被 ESLint 报告为"未定义变量"的错误
      globals: {
        ...autoImportGlobals.globals
      }
    }
  },
  globalIgnores(['**/dist/**', '**/dist-ssr/**', '**/coverage/**']),

  pluginVue.configs['flat/essential'],
  vueTsConfigs.recommended,

  {
    rules: {
      // 忽略多个单词命名规则
      'vue/multi-word-component-names': ['off'],
      '@typescript-eslint/no-explicit-any': ['off']
    }
  },

  skipFormatting
)
